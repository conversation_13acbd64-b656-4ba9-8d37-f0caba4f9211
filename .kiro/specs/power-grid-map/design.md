# 电力网络地图系统设计文档

## 概述

本设计文档描述了在现有云南省高德地图基础上构建电力网络可视化系统的技术架构和实现方案。系统将提供2D/3D地图切换、电力线路管理、电力设施图标展示等功能，采用Vue 3 + 高德地图API的技术栈。

## 架构设计

### 系统架构图

```mermaid
graph TB
    A[用户界面层] --> B[控制台组件]
    A --> C[地图组件]
    B --> D[地图模式控制]
    B --> E[图例说明]
    B --> F[线路筛选]
    C --> G[2D地图渲染]
    C --> H[3D地图渲染]
    G --> I[线路图层]
    G --> J[海量点图层]
    H --> K[3D模型加载器]
    I --> L[电力线路数据]
    J --> M[电力设施数据]
    K --> N[3D模型资源]
```

### 技术栈

- **前端框架**: Vue 3 (Composition API)
- **地图引擎**: 高德地图 JavaScript API v1.4.15
- **3D渲染**: 高德地图3D API + GLTF模型加载
- **数据管理**: 静态JSON文件
- **样式**: CSS3 + Scoped Styles

## 组件设计

### 1. 地图控制台组件 (MapControlPanel.vue)

**功能职责:**
- 提供2D/3D地图切换功能
- 显示电压等级图例说明
- 提供线路状态筛选功能

**组件结构:**
```vue
<template>
  <div class="control-panel">
    <div class="map-mode-section">
      <!-- 2D/3D切换按钮 -->
    </div>
    <div class="legend-section">
      <!-- 电压等级图例 -->
    </div>
    <div class="filter-section">
      <!-- 线路状态筛选 -->
    </div>
  </div>
</template>
```

**状态管理:**
- `mapMode`: '2D' | '3D'
- `activeFilters`: Array<'已完成' | '建设中' | '规划中'>
- `legendVisible`: boolean

### 2. 电力线路管理器 (PowerLineManager.js)

**功能职责:**
- 管理电力线路数据的加载和渲染
- 处理不同电压等级的线路样式
- 实现线路状态的动态筛选

**核心方法:**
```javascript
class PowerLineManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    this.lineGroups = new Map() // 按电压等级分组的线路
    this.activeFilters = ['已完成', '建设中', '规划中']
  }

  // 加载线路数据
  async loadPowerLines(data) {}
  
  // 渲染线路到地图
  renderLines(voltageLevel, lines) {}
  
  // 应用筛选条件
  applyFilters(filters) {}
  
  // 获取线路样式配置
  getLineStyle(voltageLevel, status) {}
}
```

**线路样式配置:**
```javascript
const LINE_STYLES = {
  voltageStyles: {
    '500KV': { strokeWeight: 8, color: '#FF4444' },
    '220KV': { strokeWeight: 6, color: '#FF4444' },
    '110KV': { strokeWeight: 4, color: '#FF4444' },
    '35KV': { strokeWeight: 3, color: '#FF4444' },
    '10KV': { strokeWeight: 2, color: '#FF4444' }
  },
  statusStyles: {
    '已完成': { strokeStyle: 'solid' },
    '建设中': { strokeStyle: 'dashed' },
    '规划中': { strokeStyle: 'dotted' }
  }
}
```

### 3. 海量点管理器 (MassMarkerManager.js)

**功能职责:**
- 管理电塔、变电站、发电站图标的海量点渲染
- 优化大量图标的性能表现
- 处理图标的动态加载和卸载

**核心实现:**
```javascript
class MassMarkerManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    this.massMarkers = new Map() // 不同类型的海量点实例
    this.markerData = {
      towers: [],
      substations: [],
      powerPlants: []
    }
  }

  // 初始化海量点
  initMassMarkers() {
    const towerMass = new this.AMap.MassMarks([], {
      opacity: 0.8,
      zIndex: 111,
      cursor: 'pointer',
      style: this.getTowerStyle()
    })
    
    this.massMarkers.set('towers', towerMass)
    this.map.add(towerMass)
  }

  // 更新海量点数据
  updateMarkers(type, data) {}
  
  // 获取图标样式
  getTowerStyle() {}
}
```

### 4. 3D模型管理器 (Model3DManager.js)

**功能职责:**
- 管理3D模式下的电塔模型加载
- 实现视野范围内的动态模型加载
- 优化3D模型的性能

**核心实现:**
```javascript
class Model3DManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    this.loadedModels = new Map() // 已加载的3D模型
    this.modelPath = '/src/assets/model/dt.gltf'
    this.visibleBounds = null
  }

  // 检查是否在3D模式且达到指定缩放级别
  shouldLoad3DModels() {
    return this.map.getViewMode_() === '3D' && this.map.getZoom() >= 12
  }

  // 加载视野范围内的3D模型
  loadModelsInView() {}
  
  // 卸载视野范围外的3D模型
  unloadModelsOutOfView() {}
  
  // 创建单个3D模型
  createModel(position, id) {}
}
```

### 5. 交互事件管理器 (InteractionManager.js)

**功能职责:**
- 管理地图上所有元素的鼠标交互事件
- 处理悬停提示框的显示和隐藏
- 管理详细信息面板的显示状态

**核心实现:**
```javascript
class InteractionManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    this.hoverTooltip = null
    this.detailPanel = null
    this.currentHoverTarget = null
  }

  // 初始化交互事件
  initInteractions() {
    this.setupLineHoverEvents()
    this.setupMarkerClickEvents()
    this.setupMapClickEvents()
  }

  // 设置线路悬停事件
  setupLineHoverEvents() {}
  
  // 设置图标点击事件
  setupMarkerClickEvents() {}
  
  // 设置地图点击事件（关闭面板）
  setupMapClickEvents() {}
  
  // 显示悬停提示框
  showHoverTooltip(data, position) {}
  
  // 隐藏悬停提示框
  hideHoverTooltip() {}
  
  // 显示详细信息面板
  showDetailPanel(data, type) {}
  
  // 隐藏详细信息面板
  hideDetailPanel() {}
}
```

### 6. 悬停提示框组件 (HoverTooltip.vue)

**功能职责:**
- 显示线路的简单信息提示
- 跟随鼠标位置动态定位
- 提供简洁的信息展示界面

**组件结构:**
```vue
<template>
  <div class="hover-tooltip" v-show="visible" :style="tooltipStyle">
    <div class="tooltip-content">
      <h4>{{ data.name }}</h4>
      <p>电压等级: {{ data.voltageLevel }}</p>
      <p>当前状态: {{ data.status }}</p>
      <p v-if="data.length">线路长度: {{ data.length }}km</p>
    </div>
  </div>
</template>
```

### 7. 详细信息面板组件 (DetailPanel.vue)

**功能职责:**
- 显示线路、电塔、变电站、发电站的详细信息
- 提供可关闭的面板界面
- 支持不同类型数据的展示模板

**组件结构:**
```vue
<template>
  <div class="detail-panel" v-show="visible">
    <div class="panel-header">
      <h3>{{ getTitle() }}</h3>
      <button class="close-btn" @click="close">×</button>
    </div>
    <div class="panel-content">
      <!-- 线路详细信息 -->
      <div v-if="type === 'powerline'" class="powerline-details">
        <div class="info-group">
          <label>基本信息</label>
          <p>线路名称: {{ data.name }}</p>
          <p>电压等级: {{ data.voltageLevel }}</p>
          <p>线路长度: {{ data.length }}km</p>
          <p>建设状态: {{ data.status }}</p>
        </div>
        <div class="info-group">
          <label>技术参数</label>
          <p>导线型号: {{ data.conductorType }}</p>
          <p>额定电流: {{ data.ratedCurrent }}A</p>
          <p>输送容量: {{ data.capacity }}MW</p>
        </div>
        <div class="info-group">
          <label>建设信息</label>
          <p>建设单位: {{ data.constructor }}</p>
          <p>投运时间: {{ data.operationDate }}</p>
          <p>维护单位: {{ data.maintainer }}</p>
        </div>
      </div>
      
      <!-- 电塔详细信息 -->
      <div v-if="type === 'tower'" class="tower-details">
        <div class="info-group">
          <label>基本信息</label>
          <p>电塔编号: {{ data.id }}</p>
          <p>电塔类型: {{ data.towerType }}</p>
          <p>所属线路: {{ data.powerlineName }}</p>
        </div>
        <div class="info-group">
          <label>位置信息</label>
          <p>经度: {{ data.longitude }}</p>
          <p>纬度: {{ data.latitude }}</p>
          <p>海拔高度: {{ data.altitude }}m</p>
        </div>
        <div class="info-group">
          <label>技术规格</label>
          <p>塔高: {{ data.height }}m</p>
          <p>基础类型: {{ data.foundationType }}</p>
          <p>维护状态: {{ data.maintenanceStatus }}</p>
        </div>
      </div>
      
      <!-- 变电站详细信息 -->
      <div v-if="type === 'substation'" class="substation-details">
        <div class="info-group">
          <label>基本信息</label>
          <p>变电站名称: {{ data.name }}</p>
          <p>电压等级: {{ data.voltageLevel }}</p>
          <p>变电站类型: {{ data.substationType }}</p>
        </div>
        <div class="info-group">
          <label>容量信息</label>
          <p>主变容量: {{ data.transformerCapacity }}MVA</p>
          <p>进线回数: {{ data.incomingLines }}</p>
          <p>出线回数: {{ data.outgoingLines }}</p>
        </div>
        <div class="info-group">
          <label>运行状态</label>
          <p>运行状态: {{ data.operationStatus }}</p>
          <p>负载率: {{ data.loadRate }}%</p>
          <p>最后检修: {{ data.lastMaintenance }}</p>
        </div>
      </div>
      
      <!-- 发电站详细信息 -->
      <div v-if="type === 'powerplant'" class="powerplant-details">
        <div class="info-group">
          <label>基本信息</label>
          <p>发电站名称: {{ data.name }}</p>
          <p>发电类型: {{ data.generationType }}</p>
          <p>所属公司: {{ data.company }}</p>
        </div>
        <div class="info-group">
          <label>装机信息</label>
          <p>装机容量: {{ data.installedCapacity }}MW</p>
          <p>机组数量: {{ data.unitCount }}</p>
          <p>投产时间: {{ data.commissionDate }}</p>
        </div>
        <div class="info-group">
          <label>运行数据</label>
          <p>当前出力: {{ data.currentOutput }}MW</p>
          <p>年发电量: {{ data.annualGeneration }}GWh</p>
          <p>运行状态: {{ data.operationStatus }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
```
```

## 数据模型设计

### 电力线路数据结构（扩展版）

```javascript
// /src/data/powerLines.js
export const powerLinesData = {
  "500KV": [
    {
      id: "500kv_001",
      name: "昆明-万象500KV线路",
      coordinates: [
        [102.712251, 25.040609], // 昆明起点
        [102.8, 25.1],           // 中间点
        [102.633333, 17.966667]  // 万象终点
      ],
      status: "已完成",
      voltageLevel: "500KV",
      
      // 基本信息（悬停显示）
      length: 1250.5, // 线路长度（公里）
      
      // 详细信息（点击显示）
      detailInfo: {
        // 技术参数
        conductorType: "ACSR-400/50",
        ratedCurrent: 1200,
        capacity: 600,
        
        // 建设信息
        constructor: "中国电建集团",
        operationDate: "2019-06-15",
        maintainer: "云南电网公司",
        
        // 运行数据
        currentLoad: 450,
        loadRate: 75,
        lastMaintenance: "2024-03-15",
        maintenanceStatus: "正常"
      },
      
      segments: [
        {
          start: 0,
          end: 1,
          status: "已完成"
        },
        {
          start: 1,
          end: 2,
          status: "建设中"
        }
      ],
      towers: [
        { 
          position: [102.712251, 25.040609], 
          id: "tower_001",
          detailInfo: {
            towerType: "直线塔",
            powerlineName: "昆明-万象500KV线路",
            longitude: 102.712251,
            latitude: 25.040609,
            altitude: 1892,
            height: 45,
            foundationType: "钢筋混凝土基础",
            maintenanceStatus: "良好"
          }
        },
        { 
          position: [102.8, 25.1], 
          id: "tower_002",
          detailInfo: {
            towerType: "转角塔",
            powerlineName: "昆明-万象500KV线路",
            longitude: 102.8,
            latitude: 25.1,
            altitude: 1956,
            height: 52,
            foundationType: "钢筋混凝土基础",
            maintenanceStatus: "良好"
          }
        }
      ]
    }
    // ... 更多线路数据
  ],
  "220KV": [
    // 220KV线路数据
  ],
  // ... 其他电压等级
}
```

### 电力设施数据结构（扩展版）

```javascript
// /src/data/powerFacilities.js
export const powerFacilitiesData = {
  substations: [
    {
      id: "sub_001",
      name: "昆明主变电站",
      position: [102.712251, 25.040609],
      voltageLevel: "500KV",
      type: "substation",
      
      // 详细信息
      detailInfo: {
        substationType: "户外变电站",
        transformerCapacity: 750, // MVA
        incomingLines: 4,
        outgoingLines: 8,
        operationStatus: "正常运行",
        loadRate: 68,
        lastMaintenance: "2024-01-20",
        commissionDate: "2018-09-10",
        maintainer: "昆明供电局"
      }
    }
    // ... 更多变电站
  ],
  powerPlants: [
    {
      id: "plant_001",
      name: "昆明发电厂",
      position: [102.8, 25.1],
      capacity: "1000MW",
      type: "powerPlant",
      
      // 详细信息
      detailInfo: {
        generationType: "燃煤发电",
        company: "华能集团",
        installedCapacity: 1000,
        unitCount: 2,
        commissionDate: "2015-12-01",
        currentOutput: 750,
        annualGeneration: 5500, // GWh
        operationStatus: "满负荷运行",
        efficiency: 42.5,
        lastMaintenance: "2024-02-10"
      }
    }
    // ... 更多发电站
  ]
}
```

## 接口设计

### 地图模式切换接口

```javascript
// 地图模式切换
interface MapModeController {
  switchTo2D(): void
  switchTo3D(): void
  getCurrentMode(): '2D' | '3D'
  onModeChange(callback: (mode: string) => void): void
}
```

### 线路筛选接口

```javascript
// 线路筛选
interface LineFilterController {
  setFilters(filters: Array<'已完成' | '建设中' | '规划中'>): void
  getActiveFilters(): Array<string>
  onFilterChange(callback: (filters: Array<string>) => void): void
}
```

## 错误处理策略

### 1. 数据加载错误
- 提供默认的空数据集
- 显示用户友好的错误提示
- 支持数据重新加载

### 2. 3D模型加载错误
- 降级到2D图标显示
- 记录错误日志
- 提供模型加载状态反馈

### 3. 地图API错误
- 实现API调用重试机制
- 提供离线模式支持
- 显示网络连接状态

## 测试策略

### 1. 单元测试
- 数据处理函数测试
- 样式计算函数测试
- 工具函数测试

### 2. 集成测试
- 地图组件集成测试
- 数据加载流程测试
- 用户交互流程测试

### 3. 性能测试
- 大量数据渲染性能测试
- 3D模型加载性能测试
- 内存使用情况监控

## 性能优化方案

### 1. 数据优化
- 使用海量点减少DOM节点数量
- 实现数据分页加载
- 采用数据缓存策略

### 2. 渲染优化
- 视野范围外的元素延迟加载
- 3D模型的LOD（细节层次）管理
- 使用requestAnimationFrame优化动画

### 3. 内存优化
- 及时清理不需要的3D模型
- 实现对象池管理
- 监控内存使用情况

## 部署和维护

### 1. 静态资源管理
- 3D模型文件的CDN部署
- 图标资源的优化压缩
- 数据文件的版本管理

### 2. 监控和日志
- 性能指标监控
- 错误日志收集
- 用户行为分析

### 3. 扩展性考虑
- 支持动态数据源接入
- 预留自定义样式接口
- 支持插件化扩展