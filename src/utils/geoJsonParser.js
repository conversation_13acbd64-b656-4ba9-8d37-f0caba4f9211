/**
 * GeoJSON数据解析工具
 * 用于从云南省GeoJSON数据中提取地名和坐标信息
 */

import yunnanGeoJson from '../assets/map/yunnan.json'

/**
 * 从GeoJSON数据中提取地名信息
 * @returns {Array} 地名信息数组
 */
export const extractPlaceNames = () => {
  try {
    if (!yunnanGeoJson || !yunnanGeoJson.features) {
      console.error('GeoJSON数据格式错误')
      return []
    }

    const placeNames = yunnanGeoJson.features.map(feature => {
      const properties = feature.properties || {}
      
      return {
        // 地区名称
        name: properties.name || '未知地区',
        // 行政区划代码
        adcode: properties.adcode,
        // 中心坐标（用于标签定位）
        center: properties.center || properties.centroid || [0, 0],
        // 质心坐标（备用）
        centroid: properties.centroid || properties.center || [0, 0],
        // 行政级别
        level: properties.level || 'unknown',
        // 子区域数量
        childrenNum: properties.childrenNum || 0,
        // 原始属性（备用）
        originalProperties: properties
      }
    })

    console.log('提取的地名信息:', placeNames)
    return placeNames

  } catch (error) {
    console.error('解析GeoJSON数据失败:', error)
    return []
  }
}

/**
 * 获取指定地区的详细信息
 * @param {string} name - 地区名称
 * @returns {Object|null} 地区详细信息
 */
export const getPlaceInfo = (name) => {
  const placeNames = extractPlaceNames()
  return placeNames.find(place => place.name === name) || null
}

/**
 * 验证坐标是否有效
 * @param {Array} coordinates - 坐标数组 [lng, lat]
 * @returns {boolean} 坐标是否有效
 */
export const isValidCoordinates = (coordinates) => {
  if (!Array.isArray(coordinates) || coordinates.length !== 2) {
    return false
  }
  
  const [lng, lat] = coordinates
  return (
    typeof lng === 'number' && 
    typeof lat === 'number' &&
    lng >= -180 && lng <= 180 &&
    lat >= -90 && lat <= 90 &&
    !isNaN(lng) && !isNaN(lat)
  )
}

/**
 * 格式化地名显示文本
 * @param {string} name - 原始地名
 * @returns {string} 格式化后的地名
 */
export const formatPlaceName = (name) => {
  if (!name || typeof name !== 'string') {
    return '未知地区'
  }
  
  // 移除常见的行政区划后缀，使标签更简洁
  return name
    .replace(/市$/, '')
    .replace(/自治州$/, '')
    .replace(/自治县$/, '')
    .replace(/县$/, '')
    .trim()
}

/**
 * 根据行政级别获取标签样式配置
 * @param {string} level - 行政级别
 * @returns {Object} 样式配置
 */
export const getPlaceNameStyle = (level) => {
  const styleMap = {
    'city': {
      fontSize: '14px',
      fontWeight: 'bold',
      color: '#2c3e50',
      backgroundColor: 'rgba(52, 152, 219, 0.9)',
      padding: '4px 8px',
      borderRadius: '4px',
      zIndex: 1000
    },
    'county': {
      fontSize: '12px',
      fontWeight: 'normal',
      color: '#34495e',
      backgroundColor: 'rgba(52, 152, 219, 0.8)',
      padding: '3px 6px',
      borderRadius: '3px',
      zIndex: 999
    },
    'default': {
      fontSize: '13px',
      fontWeight: '500',
      color: '#2c3e50',
      backgroundColor: 'rgba(52, 152, 219, 0.85)',
      padding: '4px 7px',
      borderRadius: '4px',
      zIndex: 1000
    }
  }
  
  return styleMap[level] || styleMap.default
}

// 导出默认配置
export const PLACE_NAME_CONFIG = {
  // 默认显示状态
  defaultVisible: false,
  // 最小显示缩放级别
  minZoom: 6,
  // 最大显示缩放级别  
  maxZoom: 12,
  // 标签偏移量（像素）
  offset: [0, -10],
  // 动画配置
  animation: {
    duration: 300,
    easing: 'ease-in-out'
  }
}
