/**
 * 电力线路数据管理器
 * 负责线路数据的加载、管理、验证和错误处理
 * 实现按电压等级分组的数据结构管理
 */

import { powerLinesData } from './data/powerLines.js'

export class PowerLineManager {
  constructor(map = null, AMap = null) {
    this.map = map
    this.AMap = AMap
    
    // 按电压等级分组的线路数据
    this.lineGroups = new Map()
    
    // 活动的筛选条件
    this.activeFilters = ['已完成', '建设中', '规划中']
    
    // 数据加载状态
    this.isLoaded = false
    this.loadError = null
    
    // 支持的电压等级
    this.supportedVoltages = ['500KV', '220KV', '110KV', '35KV', '10KV']
    
    // 支持的线路状态
    this.supportedStatuses = ['已完成', '建设中', '规划中']
    
    // 事件监听器
    this.eventListeners = new Map()
    
    // 初始化
    this.init()
  }

  /**
   * 初始化管理器
   */
  async init() {
    try {
      await this.loadPowerLines()
    } catch (error) {
      this.handleError('初始化失败', error)
    }
  }

  /**
   * 加载电力线路数据
   * @param {Object} data - 可选的外部数据，如果不提供则使用默认数据
   * @returns {Promise<boolean>} 加载是否成功
   */
  async loadPowerLines(data = null) {
    try {
      this.isLoaded = false
      this.loadError = null
      
      // 使用提供的数据或默认数据
      const sourceData = data || powerLinesData
      
      // 验证数据结构
      const validationResult = this.validateDataStructure(sourceData)
      if (!validationResult.isValid) {
        throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`)
      }
      
      // 清空现有数据
      this.lineGroups.clear()
      
      // 按电压等级分组加载数据
      for (const voltageLevel of this.supportedVoltages) {
        if (sourceData[voltageLevel]) {
          const lines = sourceData[voltageLevel]
          
          // 验证每条线路数据
          const validatedLines = this.validateLines(lines, voltageLevel)
          
          // 存储到分组中
          this.lineGroups.set(voltageLevel, {
            lines: validatedLines,
            count: validatedLines.length,
            visible: true,
            lastUpdated: new Date()
          })
          
          console.log(`已加载 ${voltageLevel} 线路: ${validatedLines.length} 条`)
        } else {
          console.warn(`未找到 ${voltageLevel} 电压等级的数据`)
          this.lineGroups.set(voltageLevel, {
            lines: [],
            count: 0,
            visible: true,
            lastUpdated: new Date()
          })
        }
      }
      
      this.isLoaded = true
      this.emit('dataLoaded', this.getLoadSummary())
      
      console.log('电力线路数据加载完成:', this.getLoadSummary())
      return true
      
    } catch (error) {
      this.handleError('数据加载失败', error)
      return false
    }
  }

  /**
   * 验证数据结构
   * @param {Object} data - 要验证的数据
   * @returns {Object} 验证结果
   */
  validateDataStructure(data) {
    const result = {
      isValid: true,
      errors: []
    }
    
    try {
      // 检查数据是否为对象
      if (!data || typeof data !== 'object') {
        result.errors.push('数据必须是一个对象')
        result.isValid = false
        return result
      }
      
      // 检查是否包含所有必需的电压等级
      for (const voltage of this.supportedVoltages) {
        if (!data[voltage]) {
          result.errors.push(`缺少 ${voltage} 电压等级数据`)
          result.isValid = false
        } else if (!Array.isArray(data[voltage])) {
          result.errors.push(`${voltage} 数据必须是数组格式`)
          result.isValid = false
        }
      }
      
      // 检查数据量要求（每个电压等级至少50条线路）
      for (const voltage of this.supportedVoltages) {
        if (data[voltage] && data[voltage].length < 50) {
          result.errors.push(`${voltage} 线路数量不足，至少需要50条，当前${data[voltage].length}条`)
          result.isValid = false
        }
      }
      
    } catch (error) {
      result.errors.push(`数据结构验证过程中发生错误: ${error.message}`)
      result.isValid = false
    }
    
    return result
  }

  /**
   * 验证线路数据
   * @param {Array} lines - 线路数组
   * @param {string} voltageLevel - 电压等级
   * @returns {Array} 验证后的线路数组
   */
  validateLines(lines, voltageLevel) {
    const validatedLines = []
    
    lines.forEach((line, index) => {
      try {
        const validationResult = this.validateSingleLine(line, voltageLevel, index)
        
        if (validationResult.isValid) {
          validatedLines.push(validationResult.line)
        } else {
          console.warn(`${voltageLevel} 第${index + 1}条线路验证失败:`, validationResult.errors)
        }
      } catch (error) {
        console.error(`验证 ${voltageLevel} 第${index + 1}条线路时发生错误:`, error)
      }
    })
    
    return validatedLines
  }

  /**
   * 验证单条线路数据
   * @param {Object} line - 线路数据
   * @param {string} voltageLevel - 电压等级
   * @param {number} index - 线路索引
   * @returns {Object} 验证结果
   */
  validateSingleLine(line, voltageLevel, index) {
    const result = {
      isValid: true,
      errors: [],
      line: { ...line }
    }
    
    try {
      // 检查必需字段
      const requiredFields = ['id', 'name', 'coordinates', 'status', 'segments', 'towers']
      for (const field of requiredFields) {
        if (!line[field]) {
          result.errors.push(`缺少必需字段: ${field}`)
          result.isValid = false
        }
      }
      
      // 验证ID格式
      if (line.id && !line.id.toLowerCase().startsWith(voltageLevel.toLowerCase().replace('kv', 'kv_'))) {
        result.errors.push(`线路ID格式不正确，应以 ${voltageLevel.toLowerCase().replace('kv', 'kv_')} 开头`)
      }
      
      // 验证坐标数据
      if (line.coordinates) {
        if (!Array.isArray(line.coordinates) || line.coordinates.length < 2) {
          result.errors.push('坐标数据必须是包含至少2个点的数组')
          result.isValid = false
        } else {
          // 验证每个坐标点
          line.coordinates.forEach((coord, coordIndex) => {
            if (!Array.isArray(coord) || coord.length !== 2 || 
                typeof coord[0] !== 'number' || typeof coord[1] !== 'number') {
              result.errors.push(`第${coordIndex + 1}个坐标点格式不正确`)
              result.isValid = false
            }
          })
        }
      }
      
      // 验证状态
      if (line.status && !this.supportedStatuses.includes(line.status)) {
        result.errors.push(`不支持的线路状态: ${line.status}`)
        result.isValid = false
      }
      
      // 验证分段数据
      if (line.segments) {
        if (!Array.isArray(line.segments)) {
          result.errors.push('分段数据必须是数组格式')
          result.isValid = false
        } else {
          line.segments.forEach((segment, segIndex) => {
            if (!segment.hasOwnProperty('start') || !segment.hasOwnProperty('end') || !segment.status) {
              result.errors.push(`第${segIndex + 1}个分段数据不完整`)
              result.isValid = false
            }
            if (segment.status && !this.supportedStatuses.includes(segment.status)) {
              result.errors.push(`第${segIndex + 1}个分段状态不支持: ${segment.status}`)
              result.isValid = false
            }
          })
        }
      }
      
      // 验证电塔数据
      if (line.towers) {
        if (!Array.isArray(line.towers)) {
          result.errors.push('电塔数据必须是数组格式')
          result.isValid = false
        } else if (line.coordinates && line.towers.length !== line.coordinates.length) {
          result.errors.push(`电塔数量(${line.towers.length})与坐标点数量(${line.coordinates.length})不匹配`)
          result.isValid = false
        }
      }
      
      // 添加验证时间戳
      result.line.validatedAt = new Date()
      result.line.voltageLevel = voltageLevel
      
    } catch (error) {
      result.errors.push(`验证过程中发生错误: ${error.message}`)
      result.isValid = false
    }
    
    return result
  }

  /**
   * 获取指定电压等级的线路数据
   * @param {string} voltageLevel - 电压等级
   * @returns {Array} 线路数组
   */
  getLinesByVoltage(voltageLevel) {
    if (!this.isLoaded) {
      console.warn('数据尚未加载完成')
      return []
    }
    
    const group = this.lineGroups.get(voltageLevel)
    return group ? group.lines : []
  }

  /**
   * 获取所有线路数据
   * @returns {Object} 按电压等级分组的线路数据
   */
  getAllLines() {
    if (!this.isLoaded) {
      console.warn('数据尚未加载完成')
      return {}
    }
    
    const result = {}
    this.lineGroups.forEach((group, voltageLevel) => {
      result[voltageLevel] = group.lines
    })
    return result
  }

  /**
   * 根据筛选条件获取线路数据
   * @param {Array} filters - 筛选条件数组
   * @param {string} voltageLevel - 可选的电压等级筛选
   * @returns {Object} 筛选后的线路数据
   */
  getFilteredLines(filters = null, voltageLevel = null) {
    const activeFilters = filters || this.activeFilters
    const result = {}
    
    const voltagesToProcess = voltageLevel ? [voltageLevel] : this.supportedVoltages
    
    voltagesToProcess.forEach(voltage => {
      const group = this.lineGroups.get(voltage)
      if (group && group.visible) {
        result[voltage] = group.lines.filter(line => {
          // 检查线路整体状态
          if (activeFilters.includes(line.status)) {
            return true
          }
          
          // 检查线路分段状态
          return line.segments && line.segments.some(segment => 
            activeFilters.includes(segment.status)
          )
        })
      }
    })
    
    return result
  }

  /**
   * 设置筛选条件
   * @param {Array} filters - 新的筛选条件
   */
  setFilters(filters) {
    if (!Array.isArray(filters)) {
      throw new Error('筛选条件必须是数组格式')
    }
    
    // 验证筛选条件
    const invalidFilters = filters.filter(filter => !this.supportedStatuses.includes(filter))
    if (invalidFilters.length > 0) {
      throw new Error(`不支持的筛选条件: ${invalidFilters.join(', ')}`)
    }
    
    this.activeFilters = [...filters]
    this.emit('filtersChanged', this.activeFilters)
  }

  /**
   * 获取当前筛选条件
   * @returns {Array} 当前筛选条件
   */
  getActiveFilters() {
    return [...this.activeFilters]
  }

  /**
   * 根据电压等级筛选条件获取线路数据
   * @param {Array} voltageFilters - 电压等级筛选条件数组
   * @returns {Object} 筛选后的线路数据
   */
  getVoltageFilteredLines(voltageFilters = null) {
    const result = {}

    // 如果没有提供筛选条件，返回所有可见的电压等级
    const voltagesToProcess = voltageFilters || this.supportedVoltages

    voltagesToProcess.forEach(voltage => {
      const group = this.lineGroups.get(voltage)
      if (group && group.visible) {
        // 应用当前的状态筛选条件
        result[voltage] = group.lines.filter(line => {
          // 检查线路整体状态
          if (this.activeFilters.includes(line.status)) {
            return true
          }

          // 检查线路分段状态
          return line.segments && line.segments.some(segment =>
            this.activeFilters.includes(segment.status)
          )
        })
      }
    })

    return result
  }

  /**
   * 根据组合筛选条件获取线路数据
   * @param {Array} statusFilters - 状态筛选条件数组
   * @param {Array} voltageFilters - 电压等级筛选条件数组
   * @param {Array} levelFilters - 线路级别筛选条件数组
   * @returns {Object} 筛选后的线路数据
   */
  getCombinedFilteredLines(statusFilters = null, voltageFilters = null, levelFilters = null) {
    const activeStatusFilters = statusFilters || this.activeFilters
    const activeVoltageFilters = voltageFilters || this.supportedVoltages
    const activeLevelFilters = levelFilters || ['国际', '国重', '省重', '战略', '应急', '民生', '其他']
    const result = {}

    activeVoltageFilters.forEach(voltage => {
      const group = this.lineGroups.get(voltage)
      if (group && group.visible) {
        // 应用状态和线路级别筛选条件
        result[voltage] = group.lines.filter(line => {
          // 检查线路级别筛选
          // 如果线路没有lineLevel字段或为空，归类到"其他"
          const lineLevel = line.lineLevel || '其他'
          if (!activeLevelFilters.includes(lineLevel)) {
            return false
          }

          // 检查线路整体状态
          if (activeStatusFilters.includes(line.status)) {
            return true
          }

          // 检查线路分段状态
          return line.segments && line.segments.some(segment =>
            activeStatusFilters.includes(segment.status)
          )
        })
      }
    })

    return result
  }

  /**
   * 设置电压等级的可见性
   * @param {string} voltageLevel - 电压等级
   * @param {boolean} visible - 是否可见
   */
  setVoltageVisibility(voltageLevel, visible) {
    const group = this.lineGroups.get(voltageLevel)
    if (group) {
      group.visible = visible
      this.emit('visibilityChanged', { voltageLevel, visible })
    }
  }

  /**
   * 获取数据加载摘要
   * @returns {Object} 加载摘要信息
   */
  getLoadSummary() {
    const summary = {
      isLoaded: this.isLoaded,
      totalLines: 0,
      voltageBreakdown: {},
      loadError: this.loadError
    }
    
    this.lineGroups.forEach((group, voltageLevel) => {
      summary.voltageBreakdown[voltageLevel] = {
        count: group.count,
        visible: group.visible,
        lastUpdated: group.lastUpdated
      }
      summary.totalLines += group.count
    })
    
    return summary
  }

  /**
   * 重新加载数据
   * @param {Object} newData - 可选的新数据
   * @returns {Promise<boolean>} 重新加载是否成功
   */
  async reload(newData = null) {
    console.log('重新加载电力线路数据...')
    return await this.loadPowerLines(newData)
  }

  /**
   * 清空所有数据
   */
  clear() {
    this.lineGroups.clear()
    this.isLoaded = false
    this.loadError = null
    this.emit('dataCleared')
  }

  /**
   * 错误处理
   * @param {string} message - 错误消息
   * @param {Error} error - 错误对象
   */
  handleError(message, error) {
    this.loadError = {
      message,
      error: error.message,
      timestamp: new Date()
    }
    
    console.error(`PowerLineManager错误: ${message}`, error)
    this.emit('error', this.loadError)
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * 移除事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器执行错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 获取支持的电压等级
   * @returns {Array} 支持的电压等级数组
   */
  getSupportedVoltages() {
    return [...this.supportedVoltages]
  }

  /**
   * 获取支持的状态
   * @returns {Array} 支持的状态数组
   */
  getSupportedStatuses() {
    return [...this.supportedStatuses]
  }

  /**
   * 检查数据是否已加载
   * @returns {boolean} 是否已加载
   */
  isDataLoaded() {
    return this.isLoaded
  }

  /**
   * 获取最后的错误信息
   * @returns {Object|null} 错误信息
   */
  getLastError() {
    return this.loadError
  }
}

export default PowerLineManager