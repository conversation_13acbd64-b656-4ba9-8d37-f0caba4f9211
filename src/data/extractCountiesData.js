/**
 * 数据提取脚本 - 从地图JSON文件中提取区县经纬度数据
 * 运行此脚本来生成完整的云南省区县数据
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 市州名称映射
const CITY_NAME_MAP = {
  'baoshan': '保山市',
  'chuxiong': '楚雄彝族自治州', 
  'dali': '大理白族自治州',
  'dehong': '德宏傣族景颇族自治州',
  'diqing': '迪庆藏族自治州',
  'honghe': '红河哈尼族彝族自治州',
  'kunming': '昆明市',
  'lijiang': '丽江市',
  'lincang': '临沧市',
  'nujiang': '怒江傈僳族自治州',
  'puer': '普洱市',
  'qujing': '曲靖市',
  'wenshan': '文山壮族苗族自治州',
  'xishuangbanna': '西双版纳傣族自治州',
  'yuxi': '玉溪市',
  'zhaotong': '昭通市'
}

// 市州代码映射
const CITY_CODE_MAP = {
  'kunming': '530100',
  'qujing': '530300', 
  'yuxi': '530400',
  'baoshan': '530500',
  'zhaotong': '530600',
  'lijiang': '530700',
  'puer': '530800',
  'lincang': '530900',
  'chuxiong': '532300',
  'honghe': '532500',
  'wenshan': '532600',
  'xishuangbanna': '532800',
  'dali': '532900',
  'dehong': '533100',
  'nujiang': '533300',
  'diqing': '533400'
}

/**
 * 从单个市的JSON文件中提取区县数据
 * @param {string} cityKey - 市的英文键名
 * @returns {Object} 市及其区县数据
 */
function extractCityData(cityKey) {
  try {
    const filePath = path.join(process.cwd(), 'src/assets/map/shi', `${cityKey}.json`)
    const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf8'))
    
    const cityName = CITY_NAME_MAP[cityKey]
    const cityCode = CITY_CODE_MAP[cityKey]
    
    if (!jsonData.features || !Array.isArray(jsonData.features)) {
      console.warn(`警告: ${cityKey}.json 文件格式不正确`)
      return null
    }

    // 计算市中心坐标（所有区县中心点的平均值）
    const centers = jsonData.features
      .filter(f => f.properties && f.properties.center)
      .map(f => f.properties.center)
    
    let cityCenter = [0, 0]
    if (centers.length > 0) {
      cityCenter = [
        centers.reduce((sum, c) => sum + c[0], 0) / centers.length,
        centers.reduce((sum, c) => sum + c[1], 0) / centers.length
      ]
    }

    // 提取区县数据
    const counties = jsonData.features.map(feature => {
      const props = feature.properties
      return {
        name: props.name,
        adcode: props.adcode.toString(),
        center: props.center || [0, 0],
        centroid: props.centroid || props.center || [0, 0],
        level: props.level || 'district'
      }
    }).filter(county => county.name && county.adcode)

    return {
      cityCode,
      cityName,
      cityCenter,
      counties
    }
  } catch (error) {
    console.error(`提取 ${cityKey} 数据时出错:`, error.message)
    return null
  }
}

/**
 * 提取所有市州的数据
 * @returns {Object} 完整的云南省区县数据
 */
function extractAllData() {
  const allData = {}
  
  Object.keys(CITY_NAME_MAP).forEach(cityKey => {
    console.log(`正在提取 ${CITY_NAME_MAP[cityKey]} 的数据...`)
    const cityData = extractCityData(cityKey)
    
    if (cityData) {
      allData[cityData.cityCode] = {
        cityName: cityData.cityName,
        cityCenter: cityData.cityCenter,
        counties: cityData.counties
      }
      console.log(`✓ ${cityData.cityName}: ${cityData.counties.length} 个区县`)
    } else {
      console.log(`✗ ${CITY_NAME_MAP[cityKey]}: 提取失败`)
    }
  })
  
  return allData
}

/**
 * 生成完整的数据文件内容
 * @param {Object} data - 提取的数据
 * @returns {string} 文件内容
 */
function generateDataFileContent(data) {
  const header = `/**
 * 云南省区县级经纬度数据
 * 包含云南省16个地级行政区下属的所有区县的经纬度信息
 * 数据来源：从地图JSON文件中提取
 * 生成时间：${new Date().toLocaleString('zh-CN')}
 */

// 云南省区县经纬度数据
export const yunnanCountiesData = `

  const dataContent = JSON.stringify(data, null, 2)
  
  const footer = `

// 便捷查询函数

/**
 * 根据市州代码获取区县数据
 * @param {string} cityCode - 市州代码（如：530100）
 * @returns {Object|null} 市州及其区县数据
 */
export const getCountiesByCity = (cityCode) => {
  return yunnanCountiesData[cityCode] || null
}

/**
 * 根据区县代码获取区县信息
 * @param {string} countyCode - 区县代码（如：530102）
 * @returns {Object|null} 区县信息
 */
export const getCountyByCode = (countyCode) => {
  for (const cityData of Object.values(yunnanCountiesData)) {
    const county = cityData.counties.find(c => c.adcode === countyCode)
    if (county) {
      return {
        ...county,
        cityName: cityData.cityName,
        cityCenter: cityData.cityCenter
      }
    }
  }
  return null
}

/**
 * 根据区县名称搜索区县信息
 * @param {string} countyName - 区县名称
 * @returns {Array} 匹配的区县信息数组
 */
export const searchCountiesByName = (countyName) => {
  const results = []
  for (const cityData of Object.values(yunnanCountiesData)) {
    const matchedCounties = cityData.counties.filter(c => 
      c.name.includes(countyName)
    )
    matchedCounties.forEach(county => {
      results.push({
        ...county,
        cityName: cityData.cityName,
        cityCenter: cityData.cityCenter
      })
    })
  }
  return results
}

/**
 * 获取所有区县的列表
 * @returns {Array} 所有区县信息数组
 */
export const getAllCounties = () => {
  const allCounties = []
  for (const cityData of Object.values(yunnanCountiesData)) {
    cityData.counties.forEach(county => {
      allCounties.push({
        ...county,
        cityName: cityData.cityName,
        cityCenter: cityData.cityCenter
      })
    })
  }
  return allCounties
}

/**
 * 获取所有市州的列表
 * @returns {Array} 所有市州信息数组
 */
export const getAllCities = () => {
  return Object.entries(yunnanCountiesData).map(([code, data]) => ({
    cityCode: code,
    cityName: data.cityName,
    cityCenter: data.cityCenter,
    countiesCount: data.counties.length
  }))
}

/**
 * 根据经纬度范围查找区县
 * @param {Object} bounds - 边界范围 {minLng, maxLng, minLat, maxLat}
 * @returns {Array} 范围内的区县数组
 */
export const getCountiesInBounds = (bounds) => {
  const { minLng, maxLng, minLat, maxLat } = bounds
  const results = []
  
  for (const cityData of Object.values(yunnanCountiesData)) {
    cityData.counties.forEach(county => {
      const [lng, lat] = county.center
      if (lng >= minLng && lng <= maxLng && lat >= minLat && lat <= maxLat) {
        results.push({
          ...county,
          cityName: cityData.cityName,
          cityCenter: cityData.cityCenter
        })
      }
    })
  }
  return results
}

/**
 * 计算两个坐标点之间的距离（简单欧几里得距离）
 * @param {Array} point1 - 坐标点1 [lng, lat]
 * @param {Array} point2 - 坐标点2 [lng, lat] 
 * @returns {number} 距离
 */
export const calculateDistance = (point1, point2) => {
  const [lng1, lat1] = point1
  const [lng2, lat2] = point2
  return Math.sqrt(Math.pow(lng2 - lng1, 2) + Math.pow(lat2 - lat1, 2))
}

/**
 * 根据坐标查找最近的区县
 * @param {Array} coordinates - 坐标 [lng, lat]
 * @param {number} limit - 返回结果数量限制，默认5个
 * @returns {Array} 按距离排序的区县数组
 */
export const findNearestCounties = (coordinates, limit = 5) => {
  const allCounties = getAllCounties()
  
  const countiesWithDistance = allCounties.map(county => ({
    ...county,
    distance: calculateDistance(coordinates, county.center)
  }))
  
  return countiesWithDistance
    .sort((a, b) => a.distance - b.distance)
    .slice(0, limit)
}

// 数据统计
export const getStatistics = () => {
  const cities = Object.keys(yunnanCountiesData).length
  const counties = getAllCounties().length
  
  return {
    totalCities: cities,
    totalCounties: counties,
    averageCountiesPerCity: Math.round(counties / cities * 100) / 100
  }
}

export default {
  yunnanCountiesData,
  getCountiesByCity,
  getCountyByCode,
  searchCountiesByName,
  getAllCounties,
  getAllCities,
  getCountiesInBounds,
  findNearestCounties,
  calculateDistance,
  getStatistics
}`

  return header + dataContent + footer
}

// 主函数
function main() {
  console.log('开始提取云南省区县经纬度数据...')
  console.log('=' .repeat(50))
  
  const allData = extractAllData()
  
  console.log('=' .repeat(50))
  console.log('数据提取完成，正在生成文件...')
  
  const fileContent = generateDataFileContent(allData)
  const outputPath = path.join(process.cwd(), 'src/data/yunnanCounties.js')
  
  try {
    fs.writeFileSync(outputPath, fileContent, 'utf8')
    console.log(`✓ 数据文件已生成: ${outputPath}`)
    
    // 统计信息
    const totalCities = Object.keys(allData).length
    const totalCounties = Object.values(allData).reduce((sum, city) => sum + city.counties.length, 0)
    
    console.log(`✓ 统计信息:`)
    console.log(`  - 市州总数: ${totalCities}`)
    console.log(`  - 区县总数: ${totalCounties}`)
    console.log(`  - 平均每市区县数: ${Math.round(totalCounties / totalCities * 100) / 100}`)
    
  } catch (error) {
    console.error('生成文件时出错:', error.message)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${__filename}`) {
  main()
}

export { extractAllData, generateDataFileContent }
