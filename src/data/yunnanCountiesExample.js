/**
 * 云南省区县经纬度数据使用示例
 * 展示如何使用封装的区县数据和查询函数
 */

import {
  yunnanCountiesData,
  getCountiesByCity,
  getCountyByCode,
  searchCountiesByName,
  getAllCounties,
  getAllCities,
  getCountiesInBounds,
  findNearestCounties,
  calculateDistance,
  getStatistics
} from './yunnanCountiesComplete.js'

// 使用示例函数
export const examples = {
  
  /**
   * 示例1：获取昆明市的所有区县
   */
  getKunmingCounties() {
    console.log('=== 获取昆明市的所有区县 ===')
    const kunmingData = getCountiesByCity('530100')
    if (kunmingData) {
      console.log(`${kunmingData.cityName} 共有 ${kunmingData.counties.length} 个区县：`)
      kunmingData.counties.forEach(county => {
        console.log(`- ${county.name} (${county.adcode}): [${county.center.join(', ')}]`)
      })
    }
    return kunmingData
  },

  /**
   * 示例2：根据区县代码查找区县信息
   */
  getCountyInfo() {
    console.log('\n=== 根据区县代码查找区县信息 ===')
    const county = getCountyByCode('530102') // 五华区
    if (county) {
      console.log(`区县信息：`)
      console.log(`- 名称: ${county.name}`)
      console.log(`- 代码: ${county.adcode}`)
      console.log(`- 坐标: [${county.center.join(', ')}]`)
      console.log(`- 所属市州: ${county.cityName}`)
      console.log(`- 市州中心: [${county.cityCenter.join(', ')}]`)
    }
    return county
  },

  /**
   * 示例3：搜索包含"区"字的区县
   */
  searchDistricts() {
    console.log('\n=== 搜索包含"区"字的区县 ===')
    const districts = searchCountiesByName('区')
    console.log(`找到 ${districts.length} 个区：`)
    districts.slice(0, 10).forEach(district => { // 只显示前10个
      console.log(`- ${district.name} (${district.cityName})`)
    })
    return districts
  },

  /**
   * 示例4：获取所有市州列表
   */
  getAllCitiesList() {
    console.log('\n=== 获取所有市州列表 ===')
    const cities = getAllCities()
    console.log(`云南省共有 ${cities.length} 个地级行政区：`)
    cities.forEach(city => {
      console.log(`- ${city.cityName} (${city.cityCode}): ${city.countiesCount} 个区县`)
    })
    return cities
  },

  /**
   * 示例5：获取指定经纬度范围内的区县
   */
  getCountiesInRange() {
    console.log('\n=== 获取昆明周边区县 ===')
    const bounds = {
      minLng: 102.0,
      maxLng: 103.5,
      minLat: 24.5,
      maxLat: 26.0
    }
    const countiesInBounds = getCountiesInBounds(bounds)
    console.log(`在指定范围内找到 ${countiesInBounds.length} 个区县：`)
    countiesInBounds.forEach(county => {
      console.log(`- ${county.name} (${county.cityName}): [${county.center.join(', ')}]`)
    })
    return countiesInBounds
  },

  /**
   * 示例6：查找距离昆明市中心最近的5个区县
   */
  findNearestToKunming() {
    console.log('\n=== 查找距离昆明市中心最近的5个区县 ===')
    const kunmingCenter = [102.712251, 25.040609] // 昆明市中心坐标
    const nearestCounties = findNearestCounties(kunmingCenter, 5)
    
    console.log('距离昆明市中心最近的5个区县：')
    nearestCounties.forEach((county, index) => {
      console.log(`${index + 1}. ${county.name} (${county.cityName})`)
      console.log(`   距离: ${county.distance.toFixed(4)} 度`)
      console.log(`   坐标: [${county.center.join(', ')}]`)
    })
    return nearestCounties
  },

  /**
   * 示例7：计算两个区县之间的距离
   */
  calculateDistanceBetweenCounties() {
    console.log('\n=== 计算昆明五华区和大理市之间的距离 ===')
    const wuhua = getCountyByCode('530102') // 五华区
    const dali = getCountyByCode('532901') // 大理市
    
    if (wuhua && dali) {
      const distance = calculateDistance(wuhua.center, dali.center)
      console.log(`${wuhua.name} 到 ${dali.name} 的直线距离: ${distance.toFixed(4)} 度`)
      console.log(`约等于 ${(distance * 111).toFixed(2)} 公里`) // 粗略换算
    }
    return { wuhua, dali, distance: wuhua && dali ? calculateDistance(wuhua.center, dali.center) : null }
  },

  /**
   * 示例8：获取数据统计信息
   */
  getDataStats() {
    console.log('\n=== 云南省区县数据统计 ===')
    const stats = getStatistics()
    console.log(`总市州数: ${stats.totalCities}`)
    console.log(`总区县数: ${stats.totalCounties}`)
    console.log(`平均每市区县数: ${stats.averageCountiesPerCity}`)
    return stats
  },

  /**
   * 示例9：按市州分组显示区县数量
   */
  showCountiesByCity() {
    console.log('\n=== 按市州分组显示区县数量 ===')
    const cities = getAllCities()
    const sortedCities = cities.sort((a, b) => b.countiesCount - a.countiesCount)
    
    console.log('区县数量排名（从多到少）：')
    sortedCities.forEach((city, index) => {
      console.log(`${index + 1}. ${city.cityName}: ${city.countiesCount} 个区县`)
    })
    return sortedCities
  },

  /**
   * 示例10：查找特定类型的区县（如自治县）
   */
  findAutonomousCounties() {
    console.log('\n=== 查找自治县 ===')
    const allCounties = getAllCounties()
    const autonomousCounties = allCounties.filter(county => 
      county.name.includes('自治县') || county.name.includes('自治州')
    )
    
    console.log(`云南省共有 ${autonomousCounties.length} 个自治县：`)
    autonomousCounties.forEach(county => {
      console.log(`- ${county.name} (${county.cityName})`)
    })
    return autonomousCounties
  }
}

/**
 * 运行所有示例
 */
export const runAllExamples = () => {
  console.log('🌟 云南省区县经纬度数据使用示例')
  console.log('=' .repeat(50))
  
  // 依次运行所有示例
  examples.getKunmingCounties()
  examples.getCountyInfo()
  examples.searchDistricts()
  examples.getAllCitiesList()
  examples.getCountiesInRange()
  examples.findNearestToKunming()
  examples.calculateDistanceBetweenCounties()
  examples.getDataStats()
  examples.showCountiesByCity()
  examples.findAutonomousCounties()
  
  console.log('\n✅ 所有示例运行完成！')
}

/**
 * 实用工具函数
 */
export const utils = {
  /**
   * 根据区县名称获取完整信息
   */
  getCountyFullInfo(countyName) {
    const results = searchCountiesByName(countyName)
    return results.length > 0 ? results[0] : null
  },

  /**
   * 检查坐标是否在云南省范围内（粗略判断）
   */
  isInYunnan(lng, lat) {
    // 云南省大致经纬度范围
    return lng >= 97.5 && lng <= 106.2 && lat >= 21.1 && lat <= 29.2
  },

  /**
   * 格式化坐标显示
   */
  formatCoordinates(coordinates) {
    const [lng, lat] = coordinates
    return `${lng.toFixed(6)}°E, ${lat.toFixed(6)}°N`
  },

  /**
   * 获取市州的地理中心
   */
  getCityGeographicCenter(cityCode) {
    const cityData = getCountiesByCity(cityCode)
    if (!cityData) return null

    // 计算所有区县中心点的平均值作为地理中心
    const centers = cityData.counties.map(county => county.center)
    const avgLng = centers.reduce((sum, center) => sum + center[0], 0) / centers.length
    const avgLat = centers.reduce((sum, center) => sum + center[1], 0) / centers.length
    
    return [avgLng, avgLat]
  }
}

// 默认导出
export default {
  examples,
  runAllExamples,
  utils,
  // 重新导出所有数据和函数
  yunnanCountiesData,
  getCountiesByCity,
  getCountyByCode,
  searchCountiesByName,
  getAllCounties,
  getAllCities,
  getCountiesInBounds,
  findNearestCounties,
  calculateDistance,
  getStatistics
}
