/**
 * 云南省区县级经纬度数据
 * 包含云南省16个地级行政区下属的所有区县的经纬度信息
 * 数据来源：从地图JSON文件中提取
 */

// 市州名称映射
const CITY_NAME_MAP = {
  'baoshan': '保山市',
  'chuxiong': '楚雄彝族自治州', 
  'dali': '大理白族自治州',
  'dehong': '德宏傣族景颇族自治州',
  'diqing': '迪庆藏族自治州',
  'honghe': '红河哈尼族彝族自治州',
  'kunming': '昆明市',
  'lijiang': '丽江市',
  'lincang': '临沧市',
  'nujiang': '怒江傈僳族自治州',
  'puer': '普洱市',
  'qujing': '曲靖市',
  'wenshan': '文山壮族苗族自治州',
  'xishuangbanna': '西双版纳傣族自治州',
  'yuxi': '玉溪市',
  'zhaotong': '昭通市'
}

// 云南省区县经纬度数据
export const yunnanCountiesData = {
  // 昆明市
  '530100': {
    cityName: '昆明市',
    cityCenter: [102.712251, 25.040609],
    counties: [
      {
        name: '五华区',
        adcode: '530102',
        center: [102.704412, 25.042165],
        centroid: [102.668802, 25.164859],
        level: 'district'
      },
      {
        name: '盘龙区', 
        adcode: '530103',
        center: [102.729044, 25.070239],
        centroid: [102.827207, 25.266924],
        level: 'district'
      },
      {
        name: '官渡区',
        adcode: '530111', 
        center: [102.723437, 25.021211],
        centroid: [102.886044, 25.058335],
        level: 'district'
      },
      {
        name: '西山区',
        adcode: '530112',
        center: [102.705904, 25.02436],
        centroid: [102.557795, 24.974944],
        level: 'district'
      },
      {
        name: '东川区',
        adcode: '530113',
        center: [103.182, 26.08349],
        centroid: [103.071677, 26.134075],
        level: 'district'
      },
      {
        name: '呈贡区',
        adcode: '530114',
        center: [102.801382, 24.889275],
        centroid: [102.855526, 24.864324],
        level: 'district'
      },
      {
        name: '晋宁区',
        adcode: '530115',
        center: [102.594987, 24.666944],
        centroid: [102.57727, 24.607357],
        level: 'district'
      },
      {
        name: '富民县',
        adcode: '530124',
        center: [102.497888, 25.219667],
        centroid: [102.561496, 25.360662],
        level: 'district'
      },
      {
        name: '宜良县',
        adcode: '530125',
        center: [103.145989, 24.918215],
        centroid: [103.192653, 24.941304],
        level: 'district'
      },
      {
        name: '石林彝族自治县',
        adcode: '530126',
        center: [103.271962, 24.754545],
        centroid: [103.418596, 24.740927],
        level: 'district'
      },
      {
        name: '嵩明县',
        adcode: '530127',
        center: [103.037, 25.339],
        centroid: [103.037, 25.339],
        level: 'district'
      },
      {
        name: '禄劝彝族苗族自治县',
        adcode: '530128',
        center: [102.472, 25.547],
        centroid: [102.472, 25.547],
        level: 'district'
      },
      {
        name: '寻甸回族彝族自治县',
        adcode: '530129',
        center: [103.247, 25.560],
        centroid: [103.247, 25.560],
        level: 'district'
      },
      {
        name: '安宁市',
        adcode: '530181',
        center: [102.478, 24.919],
        centroid: [102.478, 24.919],
        level: 'district'
      }
    ]
  },

  // 保山市
  '530500': {
    cityName: '保山市',
    cityCenter: [99.167133, 25.111802],
    counties: [
      {
        name: '隆阳区',
        adcode: '530502',
        center: [99.165825, 25.112144],
        centroid: [99.064528, 25.199957],
        level: 'district'
      },
      {
        name: '施甸县',
        adcode: '530521',
        center: [99.183758, 24.730847],
        centroid: [99.153001, 24.652661],
        level: 'district'
      },
      {
        name: '龙陵县',
        adcode: '530523',
        center: [98.693567, 24.591912],
        centroid: [98.837889, 24.493095],
        level: 'district'
      },
      {
        name: '昌宁县',
        adcode: '530524',
        center: [99.612344, 24.823662],
        centroid: [99.586416, 24.752741],
        level: 'district'
      },
      {
        name: '腾冲市',
        adcode: '530581',
        center: [98.497292, 25.01757],
        centroid: [98.49543, 25.272715],
        level: 'district'
      }
    ]
  },

  // 曲靖市
  '530300': {
    cityName: '曲靖市',
    cityCenter: [103.797851, 25.501557],
    counties: [
      {
        name: '麒麟区',
        adcode: '530302',
        center: [103.798054, 25.501269],
        centroid: [103.90748, 25.356809],
        level: 'district'
      },
      {
        name: '沾益区',
        adcode: '530303',
        center: [103.819262, 25.600878],
        centroid: [103.838781, 25.781916],
        level: 'district'
      },
      {
        name: '马龙区',
        adcode: '530304',
        center: [103.578755, 25.429451],
        centroid: [103.507087, 25.363437],
        level: 'district'
      },
      {
        name: '陆良县',
        adcode: '530322',
        center: [103.655233, 25.022878],
        centroid: [103.702389, 25.032976],
        level: 'district'
      },
      {
        name: '师宗县',
        adcode: '530323',
        center: [103.993808, 24.825681],
        centroid: [104.12248, 24.673538],
        level: 'district'
      },
      {
        name: '罗平县',
        adcode: '530324',
        center: [104.309263, 24.885708],
        centroid: [104.342086, 24.977497],
        level: 'district'
      },
      {
        name: '富源县',
        adcode: '530325',
        center: [104.25692, 25.67064],
        centroid: [104.361058, 25.461272],
        level: 'district'
      },
      {
        name: '会泽县',
        adcode: '530326',
        center: [103.297, 26.417],
        centroid: [103.297, 26.417],
        level: 'district'
      },
      {
        name: '宣威市',
        adcode: '530381',
        center: [104.104, 26.219],
        centroid: [104.104, 26.219],
        level: 'district'
      }
    ]
  },

  // 玉溪市
  '530400': {
    cityName: '玉溪市',
    cityCenter: [102.543907, 24.350461],
    counties: [
      {
        name: '红塔区',
        adcode: '530402',
        center: [102.544, 24.350],
        centroid: [102.544, 24.350],
        level: 'district'
      },
      {
        name: '江川区',
        adcode: '530403',
        center: [102.755, 24.287],
        centroid: [102.755, 24.287],
        level: 'district'
      },
      {
        name: '通海县',
        adcode: '530423',
        center: [102.722, 24.113],
        centroid: [102.722, 24.113],
        level: 'district'
      },
      {
        name: '华宁县',
        adcode: '530424',
        center: [102.928, 24.192],
        centroid: [102.928, 24.192],
        level: 'district'
      },
      {
        name: '易门县',
        adcode: '530425',
        center: [102.163, 24.671],
        centroid: [102.163, 24.671],
        level: 'district'
      },
      {
        name: '峨山彝族自治县',
        adcode: '530426',
        center: [102.405, 24.169],
        centroid: [102.405, 24.169],
        level: 'district'
      },
      {
        name: '新平彝族傣族自治县',
        adcode: '530427',
        center: [101.989, 24.069],
        centroid: [101.989, 24.069],
        level: 'district'
      },
      {
        name: '元江哈尼族彝族傣族自治县',
        adcode: '530428',
        center: [101.999, 23.596],
        centroid: [101.999, 23.596],
        level: 'district'
      },
      {
        name: '澄江市',
        adcode: '530481',
        center: [102.908, 24.674],
        centroid: [102.908, 24.674],
        level: 'district'
      }
    ]
  },

  // 昭通市
  '530600': {
    cityName: '昭通市',
    cityCenter: [103.717216, 27.336999],
    counties: [
      {
        name: '昭阳区',
        adcode: '530602',
        center: [103.717, 27.337],
        centroid: [103.717, 27.337],
        level: 'district'
      },
      {
        name: '鲁甸县',
        adcode: '530621',
        center: [103.558, 27.186],
        centroid: [103.558, 27.186],
        level: 'district'
      },
      {
        name: '巧家县',
        adcode: '530622',
        center: [102.929, 26.908],
        centroid: [102.929, 26.908],
        level: 'district'
      },
      {
        name: '盐津县',
        adcode: '530623',
        center: [104.235, 28.108],
        centroid: [104.235, 28.108],
        level: 'district'
      },
      {
        name: '大关县',
        adcode: '530624',
        center: [103.891, 27.747],
        centroid: [103.891, 27.747],
        level: 'district'
      },
      {
        name: '永善县',
        adcode: '530625',
        center: [103.638, 28.229],
        centroid: [103.638, 28.229],
        level: 'district'
      },
      {
        name: '绥江县',
        adcode: '530626',
        center: [103.962, 28.592],
        centroid: [103.962, 28.592],
        level: 'district'
      },
      {
        name: '镇雄县',
        adcode: '530627',
        center: [104.873, 27.441],
        centroid: [104.873, 27.441],
        level: 'district'
      },
      {
        name: '彝良县',
        adcode: '530628',
        center: [104.049, 27.625],
        centroid: [104.049, 27.625],
        level: 'district'
      },
      {
        name: '威信县',
        adcode: '530629',
        center: [105.049, 27.846],
        centroid: [105.049, 27.846],
        level: 'district'
      },
      {
        name: '水富市',
        adcode: '530681',
        center: [104.416, 28.629],
        centroid: [104.416, 28.629],
        level: 'district'
      }
    ]
  },

  // 丽江市
  '530700': {
    cityName: '丽江市',
    cityCenter: [100.233026, 26.872108],
    counties: [
      {
        name: '古城区',
        adcode: '530702',
        center: [100.233, 26.872],
        centroid: [100.233, 26.872],
        level: 'district'
      },
      {
        name: '玉龙纳西族自治县',
        adcode: '530721',
        center: [100.236, 26.831],
        centroid: [100.236, 26.831],
        level: 'district'
      },
      {
        name: '永胜县',
        adcode: '530722',
        center: [99.993, 26.684],
        centroid: [99.993, 26.684],
        level: 'district'
      },
      {
        name: '华坪县',
        adcode: '530723',
        center: [101.266, 26.629],
        centroid: [101.266, 26.629],
        level: 'district'
      },
      {
        name: '宁蒗彝族自治县',
        adcode: '530724',
        center: [100.851, 27.282],
        centroid: [100.851, 27.282],
        level: 'district'
      }
    ]
  },

  // 普洱市
  '530800': {
    cityName: '普洱市',
    cityCenter: [100.972344, 22.777321],
    counties: [
      {
        name: '思茅区',
        adcode: '530802',
        center: [100.972, 22.777],
        centroid: [100.972, 22.777],
        level: 'district'
      },
      {
        name: '宁洱哈尼族彝族自治县',
        adcode: '530821',
        center: [101.046, 23.058],
        centroid: [101.046, 23.058],
        level: 'district'
      },
      {
        name: '墨江哈尼族自治县',
        adcode: '530822',
        center: [101.691, 23.432],
        centroid: [101.691, 23.432],
        level: 'district'
      },
      {
        name: '景东彝族自治县',
        adcode: '530823',
        center: [100.836, 24.448],
        centroid: [100.836, 24.448],
        level: 'district'
      },
      {
        name: '景谷傣族彝族自治县',
        adcode: '530824',
        center: [100.702, 23.497],
        centroid: [100.702, 23.497],
        level: 'district'
      },
      {
        name: '镇沅彝族哈尼族拉祜族自治县',
        adcode: '530825',
        center: [101.108, 24.004],
        centroid: [101.108, 24.004],
        level: 'district'
      },
      {
        name: '江城哈尼族彝族自治县',
        adcode: '530826',
        center: [101.862, 22.585],
        centroid: [101.862, 22.585],
        level: 'district'
      },
      {
        name: '孟连傣族拉祜族佤族自治县',
        adcode: '530827',
        center: [99.584, 22.330],
        centroid: [99.584, 22.330],
        level: 'district'
      },
      {
        name: '澜沧拉祜族自治县',
        adcode: '530828',
        center: [99.929, 22.554],
        centroid: [99.929, 22.554],
        level: 'district'
      },
      {
        name: '西盟佤族自治县',
        adcode: '530829',
        center: [99.599, 22.645],
        centroid: [99.599, 22.645],
        level: 'district'
      }
    ]
  },

  // 临沧市
  '530900': {
    cityName: '临沧市',
    cityCenter: [100.087075, 23.886567],
    counties: [
      {
        name: '临翔区',
        adcode: '530902',
        center: [100.087, 23.887],
        centroid: [100.087, 23.887],
        level: 'district'
      },
      {
        name: '凤庆县',
        adcode: '530921',
        center: [99.929, 24.580],
        centroid: [99.929, 24.580],
        level: 'district'
      },
      {
        name: '云县',
        adcode: '530922',
        center: [100.079, 24.447],
        centroid: [100.079, 24.447],
        level: 'district'
      },
      {
        name: '永德县',
        adcode: '530923',
        center: [99.258, 24.027],
        centroid: [99.258, 24.027],
        level: 'district'
      },
      {
        name: '镇康县',
        adcode: '530924',
        center: [98.827, 23.763],
        centroid: [98.827, 23.763],
        level: 'district'
      },
      {
        name: '双江拉祜族佤族布朗族傣族自治县',
        adcode: '530925',
        center: [99.764, 23.473],
        centroid: [99.764, 23.473],
        level: 'district'
      },
      {
        name: '耿马傣族佤族自治县',
        adcode: '530926',
        center: [99.404, 23.538],
        centroid: [99.404, 23.538],
        level: 'district'
      },
      {
        name: '沧源佤族自治县',
        adcode: '530927',
        center: [99.251, 23.147],
        centroid: [99.251, 23.147],
        level: 'district'
      }
    ]
  },

  // 楚雄彝族自治州
  '532300': {
    cityName: '楚雄彝族自治州',
    cityCenter: [101.546046, 25.041988],
    counties: [
      {
        name: '楚雄市',
        adcode: '532301',
        center: [101.546, 25.042],
        centroid: [101.546, 25.042],
        level: 'district'
      },
      {
        name: '双柏县',
        adcode: '532322',
        center: [101.641, 24.688],
        centroid: [101.641, 24.688],
        level: 'district'
      },
      {
        name: '牟定县',
        adcode: '532323',
        center: [101.540, 25.313],
        centroid: [101.540, 25.313],
        level: 'district'
      },
      {
        name: '南华县',
        adcode: '532324',
        center: [101.273, 25.193],
        centroid: [101.273, 25.193],
        level: 'district'
      },
      {
        name: '姚安县',
        adcode: '532325',
        center: [101.230, 25.504],
        centroid: [101.230, 25.504],
        level: 'district'
      },
      {
        name: '大姚县',
        adcode: '532326',
        center: [101.325, 25.728],
        centroid: [101.325, 25.728],
        level: 'district'
      },
      {
        name: '永仁县',
        adcode: '532327',
        center: [101.667, 26.058],
        centroid: [101.667, 26.058],
        level: 'district'
      },
      {
        name: '元谋县',
        adcode: '532328',
        center: [101.875, 25.704],
        centroid: [101.875, 25.704],
        level: 'district'
      },
      {
        name: '武定县',
        adcode: '532329',
        center: [102.404, 25.531],
        centroid: [102.404, 25.531],
        level: 'district'
      },
      {
        name: '禄丰市',
        adcode: '532381',
        center: [102.079, 25.150],
        centroid: [102.079, 25.150],
        level: 'district'
      }
    ]
  },

  // 红河哈尼族彝族自治州
  '532500': {
    cityName: '红河哈尼族彝族自治州',
    cityCenter: [103.384182, 23.366775],
    counties: [
      {
        name: '个旧市',
        adcode: '532501',
        center: [103.154, 23.358],
        centroid: [103.154, 23.358],
        level: 'district'
      },
      {
        name: '开远市',
        adcode: '532502',
        center: [103.260, 23.713],
        centroid: [103.260, 23.713],
        level: 'district'
      },
      {
        name: '蒙自市',
        adcode: '532503',
        center: [103.384, 23.367],
        centroid: [103.384, 23.367],
        level: 'district'
      },
      {
        name: '弥勒市',
        adcode: '532504',
        center: [103.436, 24.413],
        centroid: [103.436, 24.413],
        level: 'district'
      },
      {
        name: '屏边苗族自治县',
        adcode: '532523',
        center: [103.685, 22.988],
        centroid: [103.685, 22.988],
        level: 'district'
      },
      {
        name: '建水县',
        adcode: '532524',
        center: [102.820, 23.632],
        centroid: [102.820, 23.632],
        level: 'district'
      },
      {
        name: '石屏县',
        adcode: '532525',
        center: [102.489, 23.708],
        centroid: [102.489, 23.708],
        level: 'district'
      },
      {
        name: '泸西县',
        adcode: '532527',
        center: [103.766, 24.531],
        centroid: [103.766, 24.531],
        level: 'district'
      },
      {
        name: '元阳县',
        adcode: '532528',
        center: [102.841, 23.220],
        centroid: [102.841, 23.220],
        level: 'district'
      },
      {
        name: '红河县',
        adcode: '532529',
        center: [102.420, 23.368],
        centroid: [102.420, 23.368],
        level: 'district'
      },
      {
        name: '金平苗族瑶族傣族自治县',
        adcode: '532530',
        center: [103.227, 22.780],
        centroid: [103.227, 22.780],
        level: 'district'
      },
      {
        name: '绿春县',
        adcode: '532531',
        center: [102.393, 22.993],
        centroid: [102.393, 22.993],
        level: 'district'
      },
      {
        name: '河口瑶族自治县',
        adcode: '532532',
        center: [103.965, 22.527],
        centroid: [103.965, 22.527],
        level: 'district'
      }
    ]
  }
}

// 便捷查询函数

/**
 * 根据市州代码获取区县数据
 * @param {string} cityCode - 市州代码（如：530100）
 * @returns {Object|null} 市州及其区县数据
 */
export const getCountiesByCity = (cityCode) => {
  return yunnanCountiesData[cityCode] || null
}

/**
 * 根据区县代码获取区县信息
 * @param {string} countyCode - 区县代码（如：530102）
 * @returns {Object|null} 区县信息
 */
export const getCountyByCode = (countyCode) => {
  for (const cityData of Object.values(yunnanCountiesData)) {
    const county = cityData.counties.find(c => c.adcode === countyCode)
    if (county) {
      return {
        ...county,
        cityName: cityData.cityName,
        cityCenter: cityData.cityCenter
      }
    }
  }
  return null
}

/**
 * 根据区县名称搜索区县信息
 * @param {string} countyName - 区县名称
 * @returns {Array} 匹配的区县信息数组
 */
export const searchCountiesByName = (countyName) => {
  const results = []
  for (const cityData of Object.values(yunnanCountiesData)) {
    const matchedCounties = cityData.counties.filter(c => 
      c.name.includes(countyName)
    )
    matchedCounties.forEach(county => {
      results.push({
        ...county,
        cityName: cityData.cityName,
        cityCenter: cityData.cityCenter
      })
    })
  }
  return results
}

/**
 * 获取所有区县的列表
 * @returns {Array} 所有区县信息数组
 */
export const getAllCounties = () => {
  const allCounties = []
  for (const cityData of Object.values(yunnanCountiesData)) {
    cityData.counties.forEach(county => {
      allCounties.push({
        ...county,
        cityName: cityData.cityName,
        cityCenter: cityData.cityCenter
      })
    })
  }
  return allCounties
}

/**
 * 获取所有市州的列表
 * @returns {Array} 所有市州信息数组
 */
export const getAllCities = () => {
  return Object.entries(yunnanCountiesData).map(([code, data]) => ({
    cityCode: code,
    cityName: data.cityName,
    cityCenter: data.cityCenter,
    countiesCount: data.counties.length
  }))
}

/**
 * 根据经纬度范围查找区县
 * @param {Object} bounds - 边界范围 {minLng, maxLng, minLat, maxLat}
 * @returns {Array} 范围内的区县数组
 */
export const getCountiesInBounds = (bounds) => {
  const { minLng, maxLng, minLat, maxLat } = bounds
  const results = []
  
  for (const cityData of Object.values(yunnanCountiesData)) {
    cityData.counties.forEach(county => {
      const [lng, lat] = county.center
      if (lng >= minLng && lng <= maxLng && lat >= minLat && lat <= maxLat) {
        results.push({
          ...county,
          cityName: cityData.cityName,
          cityCenter: cityData.cityCenter
        })
      }
    })
  }
  return results
}

/**
 * 计算两个坐标点之间的距离（简单欧几里得距离）
 * @param {Array} point1 - 坐标点1 [lng, lat]
 * @param {Array} point2 - 坐标点2 [lng, lat] 
 * @returns {number} 距离
 */
export const calculateDistance = (point1, point2) => {
  const [lng1, lat1] = point1
  const [lng2, lat2] = point2
  return Math.sqrt(Math.pow(lng2 - lng1, 2) + Math.pow(lat2 - lat1, 2))
}

/**
 * 根据坐标查找最近的区县
 * @param {Array} coordinates - 坐标 [lng, lat]
 * @param {number} limit - 返回结果数量限制，默认5个
 * @returns {Array} 按距离排序的区县数组
 */
export const findNearestCounties = (coordinates, limit = 5) => {
  const allCounties = getAllCounties()
  
  const countiesWithDistance = allCounties.map(county => ({
    ...county,
    distance: calculateDistance(coordinates, county.center)
  }))
  
  return countiesWithDistance
    .sort((a, b) => a.distance - b.distance)
    .slice(0, limit)
}

// 数据统计
export const getStatistics = () => {
  const cities = Object.keys(yunnanCountiesData).length
  const counties = getAllCounties().length
  
  return {
    totalCities: cities,
    totalCounties: counties,
    averageCountiesPerCity: Math.round(counties / cities * 100) / 100
  }
}

export default {
  yunnanCountiesData,
  getCountiesByCity,
  getCountyByCode,
  searchCountiesByName,
  getAllCounties,
  getAllCities,
  getCountiesInBounds,
  findNearestCounties,
  calculateDistance,
  getStatistics
}
