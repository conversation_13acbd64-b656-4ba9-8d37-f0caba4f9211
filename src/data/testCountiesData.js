/**
 * 云南省区县数据测试脚本
 * 验证数据的完整性和功能的正确性
 */

import {
  yunnanCountiesData,
  getCountiesByCity,
  getCountyByCode,
  searchCountiesByName,
  getAllCounties,
  getAllCities,
  getCountiesInBounds,
  findNearestCounties,
  calculateDistance,
  getStatistics
} from './yunnanCountiesComplete.js'

/**
 * 测试数据完整性
 */
function testDataIntegrity() {
  console.log('🔍 测试数据完整性...')
  
  const cities = getAllCities()
  const counties = getAllCounties()
  
  console.log(`✅ 市州数量: ${cities.length}`)
  console.log(`✅ 区县数量: ${counties.length}`)
  
  // 验证每个市州都有区县
  let totalCountiesFromCities = 0
  cities.forEach(city => {
    const cityData = getCountiesByCity(city.cityCode)
    if (!cityData) {
      console.error(`❌ 无法获取市州数据: ${city.cityName}`)
      return
    }
    
    if (cityData.counties.length === 0) {
      console.error(`❌ 市州无区县数据: ${city.cityName}`)
      return
    }
    
    totalCountiesFromCities += cityData.counties.length
    console.log(`  ${city.cityName}: ${cityData.counties.length} 个区县`)
  })
  
  if (totalCountiesFromCities === counties.length) {
    console.log('✅ 数据完整性验证通过')
  } else {
    console.error(`❌ 数据不一致: 预期${counties.length}个区县，实际${totalCountiesFromCities}个`)
  }
  
  return { cities: cities.length, counties: counties.length }
}

/**
 * 测试查询功能
 */
function testQueryFunctions() {
  console.log('\n🔍 测试查询功能...')
  
  // 测试根据代码查询
  const wuhua = getCountyByCode('530102')
  if (wuhua && wuhua.name === '五华区') {
    console.log('✅ 根据代码查询功能正常')
  } else {
    console.error('❌ 根据代码查询功能异常')
  }
  
  // 测试名称搜索
  const districts = searchCountiesByName('区')
  if (districts.length > 0) {
    console.log(`✅ 名称搜索功能正常，找到${districts.length}个区`)
  } else {
    console.error('❌ 名称搜索功能异常')
  }
  
  // 测试范围查询
  const bounds = {
    minLng: 102.0,
    maxLng: 103.5,
    minLat: 24.5,
    maxLat: 26.0
  }
  const countiesInBounds = getCountiesInBounds(bounds)
  if (countiesInBounds.length > 0) {
    console.log(`✅ 范围查询功能正常，找到${countiesInBounds.length}个区县`)
  } else {
    console.error('❌ 范围查询功能异常')
  }
  
  // 测试最近区县查询
  const kunmingCenter = [102.712251, 25.040609]
  const nearest = findNearestCounties(kunmingCenter, 3)
  if (nearest.length === 3) {
    console.log('✅ 最近区县查询功能正常')
    nearest.forEach((county, index) => {
      console.log(`  ${index + 1}. ${county.name} (距离: ${county.distance.toFixed(4)})`)
    })
  } else {
    console.error('❌ 最近区县查询功能异常')
  }
}

/**
 * 测试坐标数据
 */
function testCoordinates() {
  console.log('\n🔍 测试坐标数据...')
  
  const counties = getAllCounties()
  let validCoordinates = 0
  let invalidCoordinates = 0
  
  counties.forEach(county => {
    const [lng, lat] = county.center
    
    // 检查坐标是否在合理范围内（云南省大致范围）
    if (lng >= 97.5 && lng <= 106.2 && lat >= 21.1 && lat <= 29.2) {
      validCoordinates++
    } else {
      invalidCoordinates++
      console.warn(`⚠️  坐标可能异常: ${county.name} [${lng}, ${lat}]`)
    }
  })
  
  console.log(`✅ 有效坐标: ${validCoordinates}`)
  if (invalidCoordinates > 0) {
    console.log(`⚠️  异常坐标: ${invalidCoordinates}`)
  } else {
    console.log('✅ 所有坐标都在合理范围内')
  }
}

/**
 * 测试距离计算
 */
function testDistanceCalculation() {
  console.log('\n🔍 测试距离计算...')
  
  // 测试已知距离
  const kunming = [102.712251, 25.040609]
  const dali = [100.241369, 25.593067]
  
  const distance = calculateDistance(kunming, dali)
  console.log(`昆明到大理的距离: ${distance.toFixed(4)} 度`)
  console.log(`约等于: ${(distance * 111).toFixed(2)} 公里`)
  
  // 距离应该大于0
  if (distance > 0) {
    console.log('✅ 距离计算功能正常')
  } else {
    console.error('❌ 距离计算功能异常')
  }
}

/**
 * 测试统计功能
 */
function testStatistics() {
  console.log('\n🔍 测试统计功能...')
  
  const stats = getStatistics()
  console.log(`总市州数: ${stats.totalCities}`)
  console.log(`总区县数: ${stats.totalCounties}`)
  console.log(`平均每市区县数: ${stats.averageCountiesPerCity}`)
  
  if (stats.totalCities === 16 && stats.totalCounties > 100) {
    console.log('✅ 统计功能正常')
  } else {
    console.error('❌ 统计数据异常')
  }
  
  return stats
}

/**
 * 性能测试
 */
function testPerformance() {
  console.log('\n🔍 性能测试...')
  
  const iterations = 1000
  
  // 测试查询性能
  console.time('查询性能测试')
  for (let i = 0; i < iterations; i++) {
    getCountyByCode('530102')
  }
  console.timeEnd('查询性能测试')
  
  // 测试搜索性能
  console.time('搜索性能测试')
  for (let i = 0; i < 100; i++) {
    searchCountiesByName('县')
  }
  console.timeEnd('搜索性能测试')
  
  console.log('✅ 性能测试完成')
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始云南省区县数据测试')
  console.log('=' .repeat(50))
  
  try {
    const integrity = testDataIntegrity()
    testQueryFunctions()
    testCoordinates()
    testDistanceCalculation()
    const stats = testStatistics()
    testPerformance()
    
    console.log('\n' + '=' .repeat(50))
    console.log('🎉 所有测试完成！')
    console.log(`📊 数据概览: ${stats.totalCities}个市州, ${stats.totalCounties}个区县`)
    console.log('✅ 云南省区县经纬度数据封装成功！')
    
    return {
      success: true,
      stats,
      integrity
    }
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests()
}

export {
  testDataIntegrity,
  testQueryFunctions,
  testCoordinates,
  testDistanceCalculation,
  testStatistics,
  testPerformance,
  runAllTests
}
