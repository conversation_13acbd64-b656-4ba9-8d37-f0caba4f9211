/**
 * 天气API配置文件
 * 包含天气API配置和云南省各地区映射
 * 注意：真实API配置已注释，统一使用模拟数据避免API访问次数限制
 */

// 天气API配置（已注释，避免API访问次数限制）
export const WEATHER_API_CONFIG = {
  // 真实API配置已注释
  // baseUrl: 'http://101.35.2.25/api/tianqi/tqyb.php',
  // id: '10006691',
  // key: '1c6a6f2d1679b548a63fee60af92c5ad',
  // sheng: '云南'

  // 保留配置结构，但不使用真实值
  baseUrl: '',
  id: '',
  key: '',
  sheng: '云南'
}

// 云南省各地区名称映射（API中使用的地区名称）
export const YUNNAN_PLACES = [
  // 昆明市及下属区县
  '昆明', '五华', '盘龙', '官渡', '西山', '东川', '呈贡', '晋宁', '富民', '宜良', 
  '石林', '嵩明', '禄劝', '寻甸', '安宁',
  
  // 曲靖市及下属区县
  '曲靖', '麒麟', '沾益', '马龙', '陆良', '师宗', '罗平', '富源', '会泽', '宣威',
  
  // 玉溪市及下属区县
  '玉溪', '红塔', '江川', '澄江', '通海', '华宁', '易门', '峨山', '新平', '元江',
  
  // 保山市及下属区县
  '保山', '隆阳', '施甸', '龙陵', '昌宁', '腾冲',
  
  // 昭通市及下属区县
  '昭通', '昭阳', '鲁甸', '巧家', '盐津', '大关', '永善', '绥江', '镇雄', '彝良', '威信', '水富',
  
  // 丽江市及下属区县
  '丽江', '古城', '玉龙', '永胜', '华坪', '宁蒗',
  
  // 普洱市及下属区县
  '普洱', '思茅', '宁洱', '墨江', '景东', '景谷', '镇沅', '江城', '孟连', '澜沧', '西盟',
  
  // 临沧市及下属区县
  '临沧', '临翔', '凤庆', '云县', '永德', '镇康', '双江', '耿马', '沧源',
  
  // 楚雄州及下属区县
  '楚雄', '双柏', '牟定', '南华', '姚安', '大姚', '永仁', '元谋', '武定', '禄丰',
  
  // 红河州及下属区县
  '红河', '个旧', '开远', '蒙自', '屏边', '建水', '石屏', '弥勒', '泸西', '元阳', '红河县', '金平', '绿春', '河口',
  
  // 文山州及下属区县
  '文山', '砚山', '西畴', '麻栗坡', '马关', '丘北', '广南', '富宁',
  
  // 西双版纳州及下属区县
  '西双版纳', '景洪', '勐海', '勐腊',
  
  // 大理州及下属区县
  '大理', '祥云', '宾川', '弥渡', '南涧', '巍山', '永平', '云龙', '洱源', '剑川', '鹤庆', '漾濞',
  
  // 德宏州及下属区县
  '德宏', '瑞丽', '芒市', '梁河', '盈江', '陇川',
  
  // 怒江州及下属区县
  '怒江', '泸水', '福贡', '贡山', '兰坪',
  
  // 迪庆州及下属区县
  '迪庆', '香格里拉', '德钦', '维西'
]

// 地区名称标准化映射（处理API返回的地区名称差异）
export const PLACE_NAME_MAPPING = {
  // 处理一些可能的名称差异
  '昆明市': '昆明',
  '曲靖市': '曲靖',
  '玉溪市': '玉溪',
  '保山市': '保山',
  '昭通市': '昭通',
  '丽江市': '丽江',
  '普洱市': '普洱',
  '临沧市': '临沧',
  '楚雄彝族自治州': '楚雄',
  '红河哈尼族彝族自治州': '红河',
  '文山壮族苗族自治州': '文山',
  '西双版纳傣族自治州': '西双版纳',
  '大理白族自治州': '大理',
  '德宏傣族景颇族自治州': '德宏',
  '怒江傈僳族自治州': '怒江',
  '迪庆藏族自治州': '迪庆',
  '香格里拉市': '香格里拉'
}

// 热力图颜色配置
export const HEATMAP_CONFIG = {
  // 温度范围配置（摄氏度）
  temperatureRange: {
    min: -10,  // 最低温度
    max: 40    // 最高温度
  },

  // 热力图样式配置
  style: {
    radius: 50,        // 热力点半径
    opacity: [0, 0.8], // 透明度范围
    gradient: {        // 颜色梯度配置
      0.0: '#0000FF',  // 蓝色（低温）
      0.2: '#00FFFF',  // 青色
      0.4: '#00FF00',  // 绿色
      0.6: '#FFFF00',  // 黄色
      0.8: '#FF8000',  // 橙色
      1.0: '#FF0000'   // 红色（高温）
    }
  },

  // 数据点配置
  dataPoint: {
    defaultIntensity: 1,  // 默认强度
    maxIntensity: 100     // 最大强度
  }
}

// 降水量热力图配置
export const PRECIPITATION_HEATMAP_CONFIG = {
  // 降水量范围配置（毫米）
  precipitationRange: {
    min: 0,    // 最低降水量
    max: 100   // 最高降水量
  },

  // 热力图样式配置
  style: {
    radius: 50,        // 热力点半径
    opacity: [0, 0.8], // 透明度范围
    gradient: {        // 颜色梯度配置（浅蓝到深蓝）
      0.0: 'rgba(173, 216, 230, 0.1)',  // 很浅的蓝色（无降水）
      0.2: '#ADD8E6',  // 浅蓝色（小雨）
      0.4: '#87CEEB',  // 天蓝色（中雨）
      0.6: '#4682B4',  // 钢蓝色（大雨）
      0.8: '#1E90FF',  // 道奇蓝（暴雨）
      1.0: '#0000CD'   // 深蓝色（大暴雨）
    }
  },

  // 数据点配置
  dataPoint: {
    defaultIntensity: 1,  // 默认强度
    maxIntensity: 100     // 最大强度
  }
}

// API请求配置
export const API_CONFIG = {
  timeout: 10000,        // 请求超时时间（毫秒）
  retryCount: 3,         // 重试次数
  retryDelay: 1000,      // 重试延迟（毫秒）
  batchSize: 10,         // 批量请求大小
  requestInterval: 100   // 请求间隔（毫秒）
}
