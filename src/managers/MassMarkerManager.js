/**
 * 海量点管理器
 * 负责电塔、变电站、发电站图标的高性能渲染
 * 使用高德地图2.0+版本的LabelsLayer + LabelMarker API优化性能
 */

import { FACILITY_STYLES, PERFORMANCE_CONFIG, ZOOM_THRESHOLDS } from '../data/powerConfig.js'

export class MassMarkerManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap

    // 标注图层存储 - 使用2.0+版本的LabelsLayer
    this.labelsLayers = new Map() // 不同类型的标注图层

    // 标记数据存储
    this.markerData = {
      towers: [],
      substations: [],
      powerPlants: []
    }

    // 渲染状态
    this.isInitialized = false
    this.renderError = null

    // 事件监听器
    this.eventListeners = new Map()

    // 电塔筛选状态
    this.towerFiltersEnabled = true

    console.log('MassMarkerManager (2.0+版本) 初始化完成')
  }

  // 移除3D模型管理器相关方法，因为已经不再使用3D模型

  /**
   * 创建LabelMarker的统一方法，确保extData正确设置并添加点击事件
   */
  createLabelMarker(data) {
    try {
      // 分离extData和其他配置
      const { extData, ...markerConfig } = data

      // 创建LabelMarker
      const marker = new this.AMap.LabelMarker(markerConfig)

      // 单独设置extData
      if (extData) {
        marker.setExtData(extData)
      }

      // 为每个LabelMarker添加点击事件
      marker.on('click', (e) => {
        console.log('LabelMarker点击事件触发:', e)
        console.log('点击的标记:', marker)
        console.log('标记的extData:', marker.getExtData())

        const markerExtData = marker.getExtData()
        if (markerExtData) {
          // 根据类型分发事件
          switch (markerExtData.type) {
            case 'tower':
              this.handleTowerClick({ target: marker, lnglat: e.lnglat })
              break
            case 'substation':
              this.handleSubstationClick({ target: marker, lnglat: e.lnglat })
              break
            case 'powerPlant':
              this.handlePowerPlantClick({ target: marker, lnglat: e.lnglat })
              break
            default:
              console.warn('未知的标记类型:', markerExtData.type)
          }
        }
      })

      return marker
    } catch (error) {
      console.error('创建LabelMarker失败:', error, data)
      return null
    }
  }

  /**
   * 初始化海量点 - 使用2.0+版本的LabelsLayer
   */
  async initMassMarkers() {
    try {
      console.log('开始初始化海量点 (2.0+版本)...')

      // 初始化电塔标注图层
      await this.initTowerLabelsLayer()

      // 初始化变电站标注图层
      await this.initSubstationLabelsLayer()

      // 初始化发电站标注图层
      await this.initPowerPlantLabelsLayer()

      this.isInitialized = true
      this.emit('initialized', this.getInitSummary())

      console.log('海量点初始化完成 (2.0+版本)')
      return true

    } catch (error) {
      this.renderError = error
      console.error('海量点初始化失败:', error)
      this.emit('initError', error)
      return false
    }
  }

  /**
   * 初始化电塔标注图层 - 使用2.0+版本的LabelsLayer
   */
  async initTowerLabelsLayer() {
    const towerLayer = new this.AMap.LabelsLayer({
      zooms: [3, 20],
      zIndex: 111,
      allowCollision: false // 标注避让
    })

    this.labelsLayers.set('towers', towerLayer)
    this.map.add(towerLayer)

    console.log('电塔标注图层初始化完成 (2.0+版本)')
  }

  /**
   * 初始化变电站标注图层 - 使用2.0+版本的LabelsLayer
   */
  async initSubstationLabelsLayer() {
    const substationLayer = new this.AMap.LabelsLayer({
      zooms: [3, 20],
      zIndex: 112,
      allowCollision: false
    })

    this.labelsLayers.set('substations', substationLayer)
    this.map.add(substationLayer)

    console.log('变电站标注图层初始化完成 (2.0+版本)')
  }

  /**
   * 初始化发电站标注图层 - 使用2.0+版本的LabelsLayer
   */
  async initPowerPlantLabelsLayer() {
    const powerPlantLayer = new this.AMap.LabelsLayer({
      zooms: [3, 20],
      zIndex: 113,
      allowCollision: false
    })

    this.labelsLayers.set('powerPlants', powerPlantLayer)
    this.map.add(powerPlantLayer)

    console.log('发电站标注图层初始化完成 (2.0+版本)')
  }

  /**
   * 更新电塔标记数据 - 使用2.0+版本的LabelMarker
   * @param {Array} linesData - 线路数据（包含电塔位置）
   */
  updateTowerMarkers(linesData) {
    const towers = []

    // 从线路数据中提取电塔位置
    Object.values(linesData).forEach(lines => {
      lines.forEach(line => {
        if (line.towers) {
          line.towers.forEach(tower => {
            towers.push({
              name: tower.id,
              position: tower.position,
              zooms: [8, 20], // 从缩放级别8开始显示，便于测试点击功能
              opacity: 0.8,
              zIndex: 111,
              icon: this.getTowerIcon(),
              // 移除文字显示，只显示图标
              extData: {
                type: 'tower',
                towerId: tower.id,
                powerlineName: line.name,
                voltageLevel: line.voltageLevel,
                detailInfo: tower.detailInfo
              }
            })
          })
        }
      })
    })

    // 应用防重叠算法
    const optimizedTowers = this.applyAntiOverlapAlgorithm(towers, 'towers')

    // 更新标注图层数据
    const towerLayer = this.labelsLayers.get('towers')
    if (towerLayer) {
      // 清除现有标记
      towerLayer.clear()

      // 存储数据
      this.markerData.towers = optimizedTowers

      // 创建LabelMarker并添加到图层
      console.log('准备创建电塔LabelMarker，数据样例:', optimizedTowers[0])
      const labelMarkers = optimizedTowers.map((towerData, index) => {
        const marker = this.createLabelMarker(towerData)

        if (marker && index === 0) {
          console.log('成功创建第一个电塔LabelMarker:', marker)
          console.log('设置的extData:', towerData.extData)
          console.log('获取的extData:', marker.getExtData())
        }

        return marker
      }).filter(marker => marker !== null)

      console.log(`准备添加 ${labelMarkers.length} 个电塔标记到图层`)
      towerLayer.add(labelMarkers)

      console.log(`电塔标记更新完成 (2.0+版本): ${optimizedTowers.length} 个，实际创建: ${labelMarkers.length} 个`)
    }
  }

  /**
   * 更新变电站标记数据 - 使用2.0+版本的LabelMarker
   * @param {Array} substations - 变电站数据
   */
  updateSubstationMarkers(substations) {
    const markers = substations.map(station => ({
      name: station.name,
      position: station.position,
      zooms: [3, 20], // 常驻显示，从最小缩放级别开始
      opacity: 0.9,
      zIndex: 112,
      icon: this.getSubstationIcon(station.type),
      // 移除文字显示，只显示图标
      extData: {
        type: 'substation',
        substationType: station.type || 'substation', // 添加变电站类型字段用于筛选
        stationId: station.id,
        stationName: station.name,
        voltageLevel: station.voltageLevel,
        capacity: station.capacity,
        detailInfo: station.detailInfo
      }
    }))

    // 应用防重叠算法
    const optimizedMarkers = this.applyAntiOverlapAlgorithm(markers, 'substations')

    // 更新标注图层数据
    const substationLayer = this.labelsLayers.get('substations')
    if (substationLayer) {
      // 清除现有标记
      substationLayer.clear()

      // 存储数据
      this.markerData.substations = optimizedMarkers

      // 创建LabelMarker并添加到图层
      const labelMarkers = optimizedMarkers.map((stationData, index) => {
        const marker = this.createLabelMarker(stationData)

        if (marker && index === 0) {
          console.log('成功创建第一个变电站LabelMarker:', marker)
          console.log('设置的extData:', stationData.extData)
          console.log('获取的extData:', marker.getExtData())
        }

        return marker
      }).filter(marker => marker !== null)

      substationLayer.add(labelMarkers)
      console.log(`变电站标记更新完成 (2.0+版本): ${optimizedMarkers.length} 个，实际创建: ${labelMarkers.length} 个`)
    }
  }

  /**
   * 更新发电站标记数据 - 使用2.0+版本的LabelMarker
   * @param {Array} powerPlants - 发电站数据
   */
  updatePowerPlantMarkers(powerPlants) {
    const markers = powerPlants.map(plant => ({
      name: plant.name,
      position: plant.position,
      zooms: [3, 20], // 常驻显示，从最小缩放级别开始
      opacity: 0.9,
      zIndex: 113,
      icon: this.getPowerPlantIcon(plant.type),
      // 移除文字显示，只显示图标
      extData: {
        type: 'powerPlant',
        plantId: plant.id,
        plantName: plant.name,
        capacity: plant.capacity,
        generationType: plant.type,
        detailInfo: plant.detailInfo
      }
    }))

    // 应用防重叠算法
    const optimizedMarkers = this.applyAntiOverlapAlgorithm(markers, 'powerPlants')

    // 更新标注图层数据
    const powerPlantLayer = this.labelsLayers.get('powerPlants')
    if (powerPlantLayer) {
      // 清除现有标记
      powerPlantLayer.clear()

      // 存储数据
      this.markerData.powerPlants = optimizedMarkers

      // 创建LabelMarker并添加到图层
      const labelMarkers = optimizedMarkers.map((plantData, index) => {
        const marker = this.createLabelMarker(plantData)

        if (marker && index === 0) {
          console.log('成功创建第一个发电站LabelMarker:', marker)
          console.log('设置的extData:', plantData.extData)
          console.log('获取的extData:', marker.getExtData())
        }

        return marker
      }).filter(marker => marker !== null)

      powerPlantLayer.add(labelMarkers)
      console.log(`发电站标记更新完成 (2.0+版本): ${optimizedMarkers.length} 个，实际创建: ${labelMarkers.length} 个`)
    }
  }

  /**
   * 防重叠算法 - 增强版
   * @param {Array} markers - 标记数组
   * @param {string} markerType - 标记类型 ('towers', 'substations', 'powerPlants')
   * @returns {Array} 优化后的标记数组
   */
  applyAntiOverlapAlgorithm(markers, markerType = 'default') {
    // 根据标记类型设置不同的最小距离
    const minDistances = {
      towers: 0.0008,        // 电塔间距约80米
      substations: 0.002,    // 变电站间距约200米
      powerPlants: 0.003,    // 发电站间距约300米
      default: 0.001         // 默认100米
    }

    const minDistance = minDistances[markerType] || minDistances.default
    const optimized = []

    // 获取所有现有标记位置（跨类型检查）
    const allExistingPositions = this.getAllExistingPositions()

    markers.forEach(marker => {
      // 兼容新旧数据格式
      const originalPosition = marker.position || marker.lnglat
      if (!originalPosition) {
        console.warn('标记缺少位置信息:', marker)
        return
      }

      let position = [...originalPosition] // 创建副本
      let attempts = 0
      const maxAttempts = 20 // 增加尝试次数

      // 检查是否与现有标记重叠（包括其他类型的标记）
      while (attempts < maxAttempts) {
        const hasOverlap = this.checkOverlapWithAll(position, optimized, allExistingPositions, minDistance)

        if (!hasOverlap) {
          break
        }

        // 使用更智能的位置调整策略
        position = this.adjustPosition(originalPosition, attempts, minDistance)
        attempts++
      }

      optimized.push({
        ...marker,
        position: position // 使用新版本的position字段
      })
    })

    return optimized
  }

  /**
   * 获取所有现有标记位置
   * @returns {Array} 所有现有标记位置数组
   */
  getAllExistingPositions() {
    const positions = []

    // 收集所有类型的现有标记位置 - 兼容新旧格式
    this.markerData.towers?.forEach(marker => {
      const pos = marker.position || marker.lnglat
      if (pos) positions.push(pos)
    })
    this.markerData.substations?.forEach(marker => {
      const pos = marker.position || marker.lnglat
      if (pos) positions.push(pos)
    })
    this.markerData.powerPlants?.forEach(marker => {
      const pos = marker.position || marker.lnglat
      if (pos) positions.push(pos)
    })

    return positions
  }

  /**
   * 检查与所有标记的重叠
   * @param {Array} position - 检查的位置
   * @param {Array} optimized - 已优化的标记
   * @param {Array} allExisting - 所有现有位置
   * @param {number} minDistance - 最小距离
   * @returns {boolean} 是否有重叠
   */
  checkOverlapWithAll(position, optimized, allExisting, minDistance) {
    // 检查与当前批次已优化标记的重叠
    const overlapWithOptimized = optimized.some(existing => {
      const existingPos = existing.position || existing.lnglat
      if (!existingPos) return false
      const distance = this.calculateDistance(position, existingPos)
      return distance < minDistance
    })

    // 检查与所有现有标记的重叠
    const overlapWithExisting = allExisting.some(existingPos => {
      const distance = this.calculateDistance(position, existingPos)
      return distance < minDistance
    })

    return overlapWithOptimized || overlapWithExisting
  }

  /**
   * 智能位置调整
   * @param {Array} originalPos - 原始位置
   * @param {number} attempt - 尝试次数
   * @param {number} minDistance - 最小距离
   * @returns {Array} 调整后的位置
   */
  adjustPosition(originalPos, attempt, minDistance) {
    // 使用螺旋式调整策略，而不是随机调整
    const angle = (attempt * 45) % 360 // 每次旋转45度
    const radius = minDistance * (1 + attempt * 0.5) // 逐渐增大半径

    const radians = (angle * Math.PI) / 180
    const deltaX = Math.cos(radians) * radius
    const deltaY = Math.sin(radians) * radius

    return [
      originalPos[0] + deltaX,
      originalPos[1] + deltaY
    ]
  }

  /**
   * 计算两点间距离
   * @param {Array} pos1 - 位置1 [lng, lat]
   * @param {Array} pos2 - 位置2 [lng, lat]
   * @returns {number} 距离
   */
  calculateDistance(pos1, pos2) {
    const dx = pos1[0] - pos2[0]
    const dy = pos1[1] - pos2[1]
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 获取电塔样式
   * @returns {Array} 样式数组
   */
  getTowerStyle() {
    return [{
      url: '/src/assets/icon/tower.svg',
      anchor: new this.AMap.Pixel(12, 12),
      size: new this.AMap.Size(24, 24)
    }]
  }

  /**
   * 获取变电站样式
   * @returns {Array} 样式数组
   */
  getSubstationStyle() {
    return [
      {
        // 变电站
        url: '/src/assets/icon/substation.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },
      {
        // 换流站
        url: '/src/assets/icon/converter.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      }
    ]
  }

  /**
   * 获取发电站样式
   * @returns {Array} 样式数组
   */
  getPowerPlantStyle() {
    return [
      {
        // 火电/燃煤发电
        url: '/src/assets/icon/thermal.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },
      {
        // 风电
        url: '/src/assets/icon/wind.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },
      {
        // 水电
        url: '/src/assets/icon/hydro.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },
      {
        // 太阳能发电
        url: '/src/assets/icon/solar.svg',
        anchor: new this.AMap.Pixel(16, 16),
        size: new this.AMap.Size(32, 32)
      },

    ]
  }

  /**
   * 获取变电站样式索引
   * @param {string} type - 变电站类型
   * @returns {number} 样式索引
   */
  getSubstationStyleIndex(type) {
    switch (type) {
      case 'converter_station':
        return 1
      case 'substation':
      default:
        return 0
    }
  }

  /**
   * 获取电塔样式
   * @returns {Array} 样式数组
   */
  getTowerStyle() {
    return [
      {
        // 电塔
        url: '/src/assets/icon/tower.svg',
        anchor: new this.AMap.Pixel(12, 12),
        size: new this.AMap.Size(24, 24)
      }
    ]
  }

  /**
   * 获取电塔样式索引
   * @param {string} type - 电塔类型
   * @returns {number} 样式索引
   */
  getTowerStyleIndex(type) {
    return 0 // 目前只有一种电塔样式
  }

  /**
   * 获取发电站样式索引
   * @param {string} type - 发电站类型
   * @returns {number} 样式索引
   */
  getPowerPlantStyleIndex(type) {
    switch (type) {
      case 'wind':
        return 1
      case 'hydro':
        return 2
      case 'solar':
        return 3
      case 'thermal':
      default:
        return 0 // 火电/燃煤发电作为默认
    }
  }

  /**
   * 处理电塔点击事件 - 适配2.0+版本的LabelsLayer事件结构
   * @param {Object} e - 事件对象
   */
  handleTowerClick(e) {
    console.log('电塔点击事件触发:', e)
    console.log('事件对象类型:', e.type)
    console.log('事件目标:', e.target)
    console.log('事件数据:', e.data)

    // 在2.0+版本的LabelsLayer中，点击的是LabelMarker，extData通过target.getExtData()获取
    let data = null

    // 优先从target获取extData（这是2.0+版本的标准方式）
    if (e.target && typeof e.target.getExtData === 'function') {
      data = e.target.getExtData()
      console.log('从 e.target.getExtData() 获取数据:', data)
    } else if (e.data && e.data.extData) {
      data = e.data.extData
      console.log('从 e.data.extData 获取数据:', data)
    } else if (e.data) {
      data = e.data
      console.log('从 e.data 获取数据:', data)
    }

    if (!data || data.type !== 'tower') {
      console.error('电塔点击事件：无法获取有效的扩展数据或数据类型不匹配', {
        data: data,
        eventData: e.data,
        targetType: e.target?.constructor?.name
      })
      return
    }

    console.log('发出电塔点击事件:', data)
    this.emit('towerClick', {
      type: 'tower',
      data: data,
      position: e.lnglat
    })
  }

  /**
   * 处理变电站点击事件 - 适配2.0+版本的LabelsLayer事件结构
   * @param {Object} e - 事件对象
   */
  handleSubstationClick(e) {
    console.log('变电站点击事件触发:', e)
    console.log('事件对象类型:', e.type)
    console.log('事件目标:', e.target)
    console.log('事件数据:', e.data)

    // 在2.0+版本的LabelsLayer中，点击的是LabelMarker，extData通过target.getExtData()获取
    let data = null

    // 优先从target获取extData（这是2.0+版本的标准方式）
    if (e.target && typeof e.target.getExtData === 'function') {
      data = e.target.getExtData()
      console.log('从 e.target.getExtData() 获取数据:', data)
    } else if (e.data && e.data.extData) {
      data = e.data.extData
      console.log('从 e.data.extData 获取数据:', data)
    } else if (e.data) {
      data = e.data
      console.log('从 e.data 获取数据:', data)
    }

    if (!data || data.type !== 'substation') {
      console.error('变电站点击事件：无法获取有效的扩展数据或数据类型不匹配', {
        data: data,
        eventData: e.data,
        targetType: e.target?.constructor?.name
      })
      return
    }

    console.log('发出变电站点击事件:', data)
    this.emit('substationClick', {
      type: 'substation',
      data: data,
      position: e.lnglat
    })
  }

  /**
   * 处理发电站点击事件 - 适配2.0+版本的LabelsLayer事件结构
   * @param {Object} e - 事件对象
   */
  handlePowerPlantClick(e) {
    console.log('发电站点击事件触发:', e)
    console.log('事件对象类型:', e.type)
    console.log('事件目标:', e.target)
    console.log('事件数据:', e.data)

    // 在2.0+版本的LabelsLayer中，点击的是LabelMarker，extData通过target.getExtData()获取
    let data = null

    // 优先从target获取extData（这是2.0+版本的标准方式）
    if (e.target && typeof e.target.getExtData === 'function') {
      data = e.target.getExtData()
      console.log('从 e.target.getExtData() 获取数据:', data)
    } else if (e.data && e.data.extData) {
      data = e.data.extData
      console.log('从 e.data.extData 获取数据:', data)
    } else if (e.data) {
      data = e.data
      console.log('从 e.data 获取数据:', data)
    }

    if (!data || data.type !== 'powerPlant') {
      console.error('发电站点击事件：无法获取有效的扩展数据或数据类型不匹配', {
        data: data,
        eventData: e.data,
        targetType: e.target?.constructor?.name
      })
      return
    }

    console.log('发出发电站点击事件:', data)
    this.emit('powerPlantClick', {
      type: 'powerPlant',
      data: data,
      position: e.lnglat
    })
  }

  /**
   * 获取初始化摘要 - 2.0+版本
   * @returns {Object} 初始化摘要
   */
  getInitSummary() {
    return {
      isInitialized: this.isInitialized,
      labelsLayersCount: this.labelsLayers ? this.labelsLayers.size : 0,
      markerCounts: {
        towers: this.markerData.towers.length,
        substations: this.markerData.substations.length,
        powerPlants: this.markerData.powerPlants.length
      },
      renderError: this.renderError
    }
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器执行错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 根据缩放级别和3D模式控制电塔标记的显示/隐藏
   * @param {number} currentZoom - 当前缩放级别
   * @param {string} viewMode - 视图模式 ('2D' 或 '3D')
   * @param {boolean} has3DModelsLoaded - 是否有3D模型已加载（可选参数）
   */
  controlTowerMarkersVisibility(currentZoom, viewMode, has3DModelsLoaded = false) {
    try {
      const towerLayer = this.labelsLayers.get('towers')
      if (!towerLayer) {
        return
      }

      // 首先检查筛选状态
      if (!this.towerFiltersEnabled) {
        // 如果电塔筛选被禁用，直接隐藏所有2D标记
        towerLayer.clear()
        console.log('电塔筛选已禁用：隐藏所有电塔2D标记')
        return
      }

      // 检查是否达到显示电塔的最小缩放级别（修改为大于11才显示）
      if (currentZoom <= ZOOM_THRESHOLDS.SHOW_TOWERS) {
        // 缩放级别不够，不显示电塔
        towerLayer.clear()
        console.log(`缩放级别 ${currentZoom} 小于等于最小显示级别 ${ZOOM_THRESHOLDS.SHOW_TOWERS}：隐藏电塔`)
        return
      }

      // 改进的显示逻辑：
      // 1. 在2D模式下，根据缩放级别控制电塔密度
      // 2. 在3D模式下，只有当缩放级别足够且确实有3D模型加载成功时，才隐藏2D图标
      // 3. 如果3D模型加载失败，继续显示2D图标作为备用

      let shouldShowTowerMarkers = true
      let towersToShow = this.markerData.towers

      if (viewMode === '3D' && currentZoom >= ZOOM_THRESHOLDS.SHOW_3D_MODELS) {
        // 在3D模式且缩放级别足够时，检查是否有3D模型加载
        // 如果有3D模型加载成功，则隐藏2D图标；否则继续显示2D图标
        if (has3DModelsLoaded) {
          shouldShowTowerMarkers = false
          console.log(`缩放级别 ${currentZoom}，3D模式，3D模型已加载：隐藏电塔2D标记`)
        } else {
          console.log(`缩放级别 ${currentZoom}，3D模式，3D模型未加载：继续显示电塔2D标记`)
          // 根据缩放级别控制电塔密度
          towersToShow = this.getFilteredTowersByZoom(currentZoom)
        }
      } else {
        console.log(`缩放级别 ${currentZoom}，${viewMode}模式：显示电塔2D标记`)
        // 根据缩放级别控制电塔密度
        towersToShow = this.getFilteredTowersByZoom(currentZoom)
      }

      if (shouldShowTowerMarkers) {
        // 清除现有标记
        towerLayer.clear()

        // 显示电塔标记（可能经过密度筛选）
        const labelMarkers = towersToShow.map(towerData => {
          return this.createLabelMarker(towerData)
        }).filter(marker => marker !== null)
        towerLayer.add(labelMarkers)
        console.log(`显示 ${towersToShow.length} 个电塔标记（总共 ${this.markerData.towers.length} 个）`)
      } else {
        // 隐藏电塔标记
        towerLayer.clear()
      }
    } catch (error) {
      console.error('控制电塔标记显示失败:', error)
    }
  }

  /**
   * 根据缩放级别筛选电塔，控制显示密度
   * @param {number} currentZoom - 当前缩放级别
   * @returns {Array} 筛选后的电塔数组
   */
  getFilteredTowersByZoom(currentZoom) {
    if (!this.markerData.towers || this.markerData.towers.length === 0) {
      return []
    }

    // 如果缩放级别达到显示所有电塔的阈值，返回所有电塔
    if (currentZoom >= ZOOM_THRESHOLDS.SHOW_ALL_TOWERS) {
      return this.markerData.towers
    }

    // 根据缩放级别计算显示密度
    // 缩放级别越小，显示的电塔越少
    let densityRatio = 1.0

    if (currentZoom >= 11) {
      densityRatio = 0.8 // 显示80%的电塔
    } else if (currentZoom >= 10.5) {
      densityRatio = 0.6 // 显示60%的电塔
    } else if (currentZoom >= 10) {
      densityRatio = 0.4 // 显示40%的电塔
    } else {
      densityRatio = 0.2 // 显示20%的电塔
    }

    // 使用固定的间隔来选择电塔，确保分布均匀
    const step = Math.max(1, Math.floor(1 / densityRatio))
    const filteredTowers = this.markerData.towers.filter((tower, index) => {
      return index % step === 0
    })

    console.log(`缩放级别 ${currentZoom}：密度比例 ${densityRatio}，显示 ${filteredTowers.length}/${this.markerData.towers.length} 个电塔`)
    return filteredTowers
  }

  /**
   * 设置电塔标记的可见性
   * @param {boolean} visible - 是否可见
   */
  setTowerMarkersVisible(visible) {
    try {
      const towerLayer = this.labelsLayers.get('towers')
      if (!towerLayer) {
        return
      }

      if (visible) {
        // 根据当前缩放级别显示适当密度的电塔
        const currentZoom = this.map.getZoom()
        const towersToShow = this.getFilteredTowersByZoom(currentZoom)

        // 清除现有标记
        towerLayer.clear()

        // 创建并添加新标记
        const labelMarkers = towersToShow.map(towerData => {
          return this.createLabelMarker(towerData)
        }).filter(marker => marker !== null)
        towerLayer.add(labelMarkers)
        console.log(`电塔标记已显示：${towersToShow.length} 个`)
      } else {
        towerLayer.clear()
        console.log('电塔标记已隐藏')
      }
    } catch (error) {
      console.error('设置电塔标记可见性失败:', error)
    }
  }

  /**
   * 清除所有标记 - 2.0+版本
   */
  clearAllMarkers() {
    // 清空所有标注图层数据
    this.labelsLayers.forEach(layer => {
      layer.clear()
    })

    // 清空数据存储
    this.markerData = { towers: [], substations: [], powerPlants: [] }

    console.log('所有标记已清除')
  }

  /**
   * 应用变电站筛选
   * @param {Array} activeFilters - 激活的变电站类型筛选器
   */
  async applySubstationFilters(activeFilters) {
    try {
      console.log('应用变电站筛选 (2.0+版本):', activeFilters)

      const substationLayer = this.labelsLayers.get('substations')
      if (!substationLayer) {
        console.warn('变电站标注图层不存在')
        return
      }

      // 清除现有标记
      substationLayer.clear()

      // 如果没有筛选条件，隐藏所有变电站
      if (!activeFilters || activeFilters.length === 0) {
        console.log('变电站筛选：隐藏所有变电站')
        return
      }

      // 根据筛选条件过滤变电站数据
      const filteredSubstations = this.markerData.substations.filter(marker => {
        const substationType = marker.extData?.substationType || 'substation'
        return activeFilters.includes(substationType)
      })

      // 创建LabelMarker并添加到图层
      const labelMarkers = filteredSubstations.map(stationData => {
        return this.createLabelMarker(stationData)
      }).filter(marker => marker !== null)

      substationLayer.add(labelMarkers)
      console.log(`变电站筛选完成 (2.0+版本): 显示 ${filteredSubstations.length}/${this.markerData.substations.length} 个变电站`)

    } catch (error) {
      console.error('应用变电站筛选失败:', error)
    }
  }

  /**
   * 应用发电站筛选
   * @param {Array} activeFilters - 激活的发电站类型筛选器
   */
  async applyPowerPlantFilters(activeFilters) {
    try {
      console.log('应用发电站筛选 (2.0+版本):', activeFilters)

      const powerPlantLayer = this.labelsLayers.get('powerPlants')
      if (!powerPlantLayer) {
        console.warn('发电站标注图层不存在')
        return
      }

      // 清除现有标记
      powerPlantLayer.clear()

      // 如果没有筛选条件，隐藏所有发电站
      if (!activeFilters || activeFilters.length === 0) {
        console.log('发电站筛选：隐藏所有发电站')
        return
      }

      // 根据筛选条件过滤发电站数据
      const filteredPowerPlants = this.markerData.powerPlants.filter(marker => {
        const plantType = marker.extData?.generationType || marker.extData?.type
        return activeFilters.includes(plantType)
      })

      // 创建LabelMarker并添加到图层
      const labelMarkers = filteredPowerPlants.map(plantData => {
        return this.createLabelMarker(plantData)
      }).filter(marker => marker !== null)

      powerPlantLayer.add(labelMarkers)
      console.log(`发电站筛选完成 (2.0+版本): 显示 ${filteredPowerPlants.length}/${this.markerData.powerPlants.length} 个发电站`)

    } catch (error) {
      console.error('应用发电站筛选失败:', error)
    }
  }

  /**
   * 应用电塔筛选 - 2.0+版本（移除3D模型相关代码）
   * @param {Array} activeFilters - 激活的电塔类型筛选器
   */
  async applyTowerFilters(activeFilters) {
    try {
      console.log('🔄 应用电塔筛选 (2.0+版本):', activeFilters)

      // 更新筛选状态
      this.towerFiltersEnabled = activeFilters && activeFilters.length > 0 && activeFilters.includes('tower')
      console.log(`📊 电塔筛选状态: ${this.towerFiltersEnabled ? '启用' : '禁用'}`)

      const towerLayer = this.labelsLayers.get('towers')
      if (!towerLayer) {
        console.warn('⚠️ 电塔标注图层不存在')
        return
      }

      // 清除现有标记
      towerLayer.clear()

      // 根据筛选状态控制标记显示
      if (!this.towerFiltersEnabled) {
        console.log('🚫 电塔筛选：隐藏所有电塔标记')
      } else {
        // 显示电塔标记
        const labelMarkers = this.markerData.towers.map(towerData => {
          return this.createLabelMarker(towerData)
        }).filter(marker => marker !== null)

        towerLayer.add(labelMarkers)
        console.log('✅ 电塔筛选：显示电塔标记')
      }

      console.log(`✅ 电塔筛选完成 (2.0+版本): ${this.towerFiltersEnabled ? '显示' : '隐藏'} 电塔`)

    } catch (error) {
      console.error('❌ 应用电塔筛选失败:', error)
    }
  }

  /**
   * 重置所有设施筛选（显示所有设施）- 2.0+版本
   */
  resetAllFacilityFilters() {
    try {
      console.log('重置所有设施筛选 (2.0+版本)')

      // 重置变电站显示
      const substationLayer = this.labelsLayers.get('substations')
      if (substationLayer && this.markerData.substations.length > 0) {
        substationLayer.clear()
        const labelMarkers = this.markerData.substations.map(stationData => {
          return this.createLabelMarker(stationData)
        }).filter(marker => marker !== null)
        substationLayer.add(labelMarkers)
      }

      // 重置发电站显示
      const powerPlantLayer = this.labelsLayers.get('powerPlants')
      if (powerPlantLayer && this.markerData.powerPlants.length > 0) {
        powerPlantLayer.clear()
        const labelMarkers = this.markerData.powerPlants.map(plantData => {
          return this.createLabelMarker(plantData)
        }).filter(marker => marker !== null)
        powerPlantLayer.add(labelMarkers)
      }

      // 重置电塔显示
      const towerLayer = this.labelsLayers.get('towers')
      if (towerLayer && this.markerData.towers.length > 0) {
        towerLayer.clear()
        const labelMarkers = this.markerData.towers.map(towerData => {
          return this.createLabelMarker(towerData)
        }).filter(marker => marker !== null)
        towerLayer.add(labelMarkers)
      }

      console.log('所有设施筛选已重置 (2.0+版本)')
    } catch (error) {
      console.error('重置设施筛选失败:', error)
    }
  }

  /**
   * 销毁管理器 - 适配2.0+版本
   */
  destroy() {
    // 从地图中移除所有标注图层
    this.labelsLayers.forEach(layer => {
      this.map.remove(layer)
    })

    this.labelsLayers.clear()
    this.markerData = { towers: [], substations: [], powerPlants: [] }
    this.eventListeners.clear()
    this.map = null
    this.AMap = null

    console.log('MassMarkerManager (2.0+版本) 已销毁')
  }

  /**
   * 获取地图视图模式
   * @returns {string} 视图模式
   */
  getViewMode() {
    try {
      if (typeof this.map.getViewMode === 'function') {
        return this.map.getViewMode()
      } else if (typeof this.map.getViewMode_ === 'function') {
        return this.map.getViewMode_()
      } else {
        // 通过其他方式判断是否为3D模式
        const pitch = this.map.getPitch ? this.map.getPitch() : 0
        return pitch > 0 ? '3D' : '2D'
      }
    } catch (error) {
      console.warn('获取视图模式失败:', error)
      return '2D'
    }
  }

  // ===== 2.0+版本的图标和文本样式方法 =====

  /**
   * 获取电塔图标配置 - 2.0+版本
   * @returns {Object} 图标配置
   */
  getTowerIcon() {
    return {
      type: 'image',
      image: '/src/assets/icon/tower.svg',
      size: [24, 24],
      anchor: 'center',
      clipOrigin: [0, 0],
      clipSize: [24, 24]
    }
  }

  /**
   * 获取电塔文本样式 - 2.0+版本
   * @returns {Object} 文本样式
   */
  getTowerTextStyle() {
    return {
      fontSize: 12,
      fontWeight: 'normal',
      fillColor: '#22886f',
      strokeColor: '#fff',
      strokeWidth: 2,
      fold: true,
      padding: '2, 5'
    }
  }

  /**
   * 获取变电站图标配置 - 2.0+版本
   * @param {string} type - 变电站类型
   * @returns {Object} 图标配置
   */
  getSubstationIcon(type) {
    let iconUrl = '/src/assets/icon/substation.svg'
    if (type === 'converter_station') {
      iconUrl = '/src/assets/icon/converter.svg'
    }

    return {
      type: 'image',
      image: iconUrl,
      size: [32, 32],
      anchor: 'center'
    }
  }

  /**
   * 获取变电站文本样式 - 2.0+版本
   * @returns {Object} 文本样式
   */
  getSubstationTextStyle() {
    return {
      fontSize: 12,
      fontWeight: 'normal',
      fillColor: '#1890ff',
      strokeColor: '#fff',
      strokeWidth: 2,
      fold: true,
      padding: '2, 5'
    }
  }

  /**
   * 获取发电站图标配置 - 2.0+版本
   * @param {string} type - 发电站类型
   * @returns {Object} 图标配置
   */
  getPowerPlantIcon(type) {
    let iconUrl = '/src/assets/icon/thermal.svg'
    switch (type) {
      case 'wind':
        iconUrl = '/src/assets/icon/wind.svg'
        break
      case 'hydro':
        iconUrl = '/src/assets/icon/hydro.svg'
        break
      case 'solar':
        iconUrl = '/src/assets/icon/solar.svg'
        break
      case 'nuclear':
        iconUrl = '/src/assets/icon/nuclear.svg'
        break
      default:
        iconUrl = '/src/assets/icon/thermal.svg'
    }

    return {
      type: 'image',
      image: iconUrl,
      size: [32, 32],
      anchor: 'center'
    }
  }

  /**
   * 获取发电站文本样式 - 2.0+版本
   * @returns {Object} 文本样式
   */
  getPowerPlantTextStyle() {
    return {
      fontSize: 12,
      fontWeight: 'normal',
      fillColor: '#f5222d',
      strokeColor: '#fff',
      strokeWidth: 2,
      fold: true,
      padding: '2, 5'
    }
  }
}

export default MassMarkerManager
