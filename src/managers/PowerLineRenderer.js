/**
 * 电力线路渲染器 - 完全照搬1.4版本实现并适配2.0 API
 * 负责在地图上渲染电力线路，支持不同电压等级的样式和状态
 * 提供完整的事件处理和交互功能
 */

import { LINE_STYLES, LINE_STATUS_CONFIG } from '../data/powerConfig.js'

export class PowerLineRenderer {
  constructor(map, AMap, weatherManager = null) {
    this.map = map
    this.AMap = AMap
    this.weatherManager = weatherManager // 天气管理器引用

    // 渲染的线路对象存储 - 照搬1.4版本结构
    this.renderedLines = new Map() // 按电压等级分组存储
    this.lineObjects = new Map() // 存储实际的地图线路对象，用于事件绑定

    // 当前激活的筛选条件 - 照搬1.4版本
    this.activeFilters = ['已完成', '建设中', '规划中'] // 状态筛选
    this.activeVoltageFilters = ['500KV', '220KV', '110KV', '35KV', '10KV'] // 电压等级筛选
    this.activeLevelFilters = ['国际', '国重', '省重', '战略', '应急', '民生', '其他'] // 线路级别筛选

    // 渲染状态
    this.isRendering = false
    this.renderError = null

    // 事件监听器 - 照搬1.4版本事件系统
    this.eventListeners = new Map()

    console.log('PowerLineRenderer 初始化完成 (照搬1.4版本实现)')
  }

  /**
   * 渲染所有线路数据 - 照搬1.4版本实现
   * @param {Object} linesData - 按电压等级分组的线路数据
   * @param {Array} filters - 筛选条件
   * @returns {Promise<boolean>} 渲染是否成功
   */
  async renderAllLines(linesData, filters = null) {
    try {
      this.isRendering = true
      this.renderError = null

      // 更新筛选条件
      if (filters) {
        this.activeFilters = filters
      }

      console.log('开始渲染电力线路，筛选条件:', this.activeFilters)

      // 清除现有线路
      this.clearAllLines()

      // 按电压等级渲染线路
      for (const [voltageLevel, lines] of Object.entries(linesData)) {
        if (lines && lines.length > 0) {
          await this.renderLinesByVoltage(voltageLevel, lines)
        }
      }

      this.isRendering = false
      this.emit('renderComplete', this.getRenderSummary())

      return true

    } catch (error) {
      this.isRendering = false
      this.renderError = error

      this.emit('renderError', error)
      return false
    }
  }

  /**
   * 按电压等级渲染线路 - 照搬1.4版本实现
   * @param {string} voltageLevel - 电压等级
   * @param {Array} lines - 线路数组
   */
  async renderLinesByVoltage(voltageLevel, lines) {
    // 检查电压等级是否在筛选范围内
    if (!this.activeVoltageFilters.includes(voltageLevel)) {

      return
    }

    const filteredLines = this.filterLines(lines)

    if (filteredLines.length === 0) {

      return
    }

    const renderedObjects = []

    for (const line of filteredLines) {
      try {
        const lineObjects = await this.renderSingleLine(line, voltageLevel)
        renderedObjects.push(...lineObjects)
      } catch (error) {

      }
    }

    // 存储渲染的线路对象
    this.renderedLines.set(voltageLevel, filteredLines)
    this.lineObjects.set(voltageLevel, renderedObjects)
    
  }

  /**
   * 渲染单条线路 - 照搬1.4版本实现
   * @param {Object} line - 线路数据
   * @param {string} voltageLevel - 电压等级
   * @returns {Array} 渲染的线路对象数组
   */
  async renderSingleLine(line, voltageLevel) {
    const lineObjects = []

    try {
      // 检查线路是否有分段数据
      if (line.segments && line.segments.length > 0) {
        // 按分段渲染
        for (const segment of line.segments) {
          const segmentPolyline = this.createSegmentPolyline(line, segment, voltageLevel)
          if (segmentPolyline) {
            segmentPolyline.setMap(this.map) // 使用1.4版本的添加方式
            lineObjects.push(segmentPolyline)
          }
        }
      } else {
        // 整条线路渲染
        const polyline = this.createLinePolyline(line, voltageLevel)
        if (polyline) {
          polyline.setMap(this.map) // 使用1.4版本的添加方式
          lineObjects.push(polyline)
        }
      }

      console.log(`线路 ${line.name} 渲染完成，共 ${lineObjects.length} 个对象`)

    } catch (error) {
      console.error(`渲染线路 ${line.name} 失败:`, error)
    }

    return lineObjects
  }

  /**
   * 创建线路折线对象 - 照搬1.4版本实现
   * @param {Object} line - 线路数据
   * @param {string} voltageLevel - 电压等级
   * @returns {Object} 折线对象
   */
  createLinePolyline(line, voltageLevel) {
    const style = this.getLineStyle(line, voltageLevel)
    
    // 动态获取天气数据
    const weatherData = this.getWeatherDataForLine(line.coordinates)

    const polyline = new this.AMap.Polyline({
      path: line.coordinates,
      strokeColor: style.strokeColor,
      strokeWeight: style.strokeWeight,
      strokeOpacity: style.strokeOpacity,
      strokeStyle: style.strokeStyle,
      strokeDasharray: style.strokeDasharray,
      zIndex: style.zIndex,
      cursor: 'pointer',
      // 存储线路数据用于交互 - 照搬1.4版本数据结构
      extData: {
        type: 'powerline',
        lineId: line.id,
        lineName: line.name,
        voltageLevel: voltageLevel,
        status: line.status,
        length: line.length,
        lineLevel: line.lineLevel,
        temperature: weatherData?.temperature || null, // 动态获取气温数据
        weather: weatherData?.weather || null, // 动态获取天气数据
        precipitation: weatherData?.precipitation || null, // 动态获取降水量数据
        weatherPlace: weatherData?.place || null, // 天气数据来源地区
        weatherDistance: weatherData?.distance || null, // 距离天气站的距离
        constructionProgress: line.constructionProgress,
        segments: line.segments,
        description: line.description,
        detailInfo: line.detailInfo
      }
    })
    
    return polyline
  }

  /**
   * 创建分段线路折线对象 - 照搬1.4版本实现
   * @param {Object} line - 线路数据
   * @param {Object} segment - 分段数据
   * @param {string} voltageLevel - 电压等级
   * @returns {Object} 折线对象
   */
  createSegmentPolyline(line, segment, voltageLevel) {
    const segmentCoords = line.coordinates.slice(segment.start, segment.end + 1)
    const style = this.getSegmentStyle(line, segment, voltageLevel)

    // 动态获取天气数据（使用分段坐标）
    const weatherData = this.getWeatherDataForLine(segmentCoords)

    const polyline = new this.AMap.Polyline({
      path: segmentCoords,
      strokeColor: style.strokeColor,
      strokeWeight: style.strokeWeight,
      strokeOpacity: style.strokeOpacity,
      strokeStyle: style.strokeStyle,
      strokeDasharray: style.strokeDasharray,
      zIndex: style.zIndex,
      cursor: 'pointer',
      // 存储线路数据用于交互 - 照搬1.4版本数据结构
      extData: {
        type: 'powerline',
        lineId: line.id,
        lineName: line.name,
        voltageLevel: voltageLevel,
        status: segment.status,
        length: line.length,
        lineLevel: line.lineLevel,
        temperature: weatherData?.temperature || null, // 动态获取气温数据
        weather: weatherData?.weather || null, // 动态获取天气数据
        precipitation: weatherData?.precipitation || null, // 动态获取降水量数据
        weatherPlace: weatherData?.place || null, // 天气数据来源地区
        weatherDistance: weatherData?.distance || null, // 距离天气站的距离
        constructionProgress: line.constructionProgress,
        segments: line.segments,
        description: line.description,
        segmentIndex: line.segments.indexOf(segment),
        detailInfo: line.detailInfo
      }
    })
    
    return polyline
  }

  /**
   * 获取线路样式 - 照搬1.4版本实现，适配当前配置结构
   * @param {Object} line - 线路数据
   * @param {string} voltageLevel - 电压等级
   * @returns {Object} 样式配置
   */
  getLineStyle(line, voltageLevel) {
    // 适配当前配置结构
    const voltageStyle = LINE_STYLES.voltageStyles[voltageLevel] || LINE_STYLES.voltageStyles['500KV']
    const statusStyle = LINE_STYLES.statusStyles[line.status] || LINE_STYLES.statusStyles['已完成']

    return {
      strokeColor: statusStyle.color || voltageStyle.color,
      strokeWeight: voltageStyle.strokeWeight || 4,
      strokeOpacity: 0.8,
      strokeStyle: statusStyle.strokeStyle || 'solid',
      strokeDasharray: statusStyle.strokeStyle === 'dashed' ? [10, 5] :
                      statusStyle.strokeStyle === 'dotted' ? [2, 2] : undefined,
      zIndex: 100 + voltageStyle.priority
    }
  }

  /**
   * 获取分段样式 - 照搬1.4版本实现，适配当前配置结构
   * @param {Object} line - 线路数据
   * @param {Object} segment - 分段数据
   * @param {string} voltageLevel - 电压等级
   * @returns {Object} 样式配置
   */
  getSegmentStyle(line, segment, voltageLevel) {
    // 适配当前配置结构
    const voltageStyle = LINE_STYLES.voltageStyles[voltageLevel] || LINE_STYLES.voltageStyles['500KV']
    const statusStyle = LINE_STYLES.statusStyles[segment.status] || LINE_STYLES.statusStyles['已完成']

    return {
      strokeColor: statusStyle.color || voltageStyle.color,
      strokeWeight: voltageStyle.strokeWeight || 4,
      strokeOpacity: 0.8,
      strokeStyle: statusStyle.strokeStyle || 'solid',
      strokeDasharray: statusStyle.strokeStyle === 'dashed' ? [10, 5] :
                      statusStyle.strokeStyle === 'dotted' ? [2, 2] : undefined,
      zIndex: 100 + voltageStyle.priority
    }
  }

  /**
   * 过滤线路数据 - 照搬1.4版本实现，修复线路级别筛选逻辑
   * @param {Array} lines - 线路数组
   * @returns {Array} 过滤后的线路数组
   */
  filterLines(lines) {
    return lines.filter(line => {
      // 状态筛选
      if (!this.activeFilters.includes(line.status)) {
        return false
      }

      // 线路级别筛选 - 如果线路没有lineLevel字段或为空，归类到"其他"
      const lineLevel = line.lineLevel || '其他'
      if (!this.activeLevelFilters.includes(lineLevel)) {
        return false
      }

      return true
    })
  }

  /**
   * 清除所有线路 - 照搬1.4版本实现
   */
  clearAllLines() {
    try {
      // 清除地图上的线路对象
      this.lineObjects.forEach((lineObjectArray) => {
        lineObjectArray.forEach(lineObj => {
          if (lineObj && lineObj.setMap) {
            lineObj.setMap(null)
          }
        })
      })

      // 清空存储
      this.renderedLines.clear()
      this.lineObjects.clear()

      console.log('所有线路已清除')

    } catch (error) {
      console.error('清除线路失败:', error)
    }
  }

  /**
   * 更新筛选条件 - 照搬1.4版本实现
   * @param {Object} filters - 筛选条件
   */
  updateFilters(filters) {
    if (filters.status) {
      this.activeFilters = filters.status
    }
    if (filters.voltageLevel) {
      this.activeVoltageFilters = filters.voltageLevel
    }
    if (filters.lineLevel) {
      this.activeLevelFilters = filters.lineLevel
    }

    console.log('筛选条件已更新:', {
      status: this.activeFilters,
      voltageLevel: this.activeVoltageFilters,
      lineLevel: this.activeLevelFilters
    })
  }

  /**
   * 获取渲染摘要 - 照搬1.4版本实现
   * @returns {Object} 渲染摘要
   */
  getRenderSummary() {
    let totalLines = 0
    let totalObjects = 0

    this.renderedLines.forEach((lines) => {
      totalLines += lines.length
    })

    this.lineObjects.forEach((objects) => {
      totalObjects += objects.length
    })

    return {
      totalLines,
      totalObjects,
      voltageBreakdown: this.getVoltageBreakdown(),
      statusBreakdown: this.getStatusBreakdown()
    }
  }

  /**
   * 获取电压等级分布 - 照搬1.4版本实现
   * @returns {Object} 电压等级分布
   */
  getVoltageBreakdown() {
    const breakdown = {}
    this.renderedLines.forEach((lines, voltageLevel) => {
      breakdown[voltageLevel] = lines.length
    })
    return breakdown
  }

  /**
   * 获取状态分布 - 照搬1.4版本实现
   * @returns {Object} 状态分布
   */
  getStatusBreakdown() {
    const breakdown = {}
    this.renderedLines.forEach((lines) => {
      lines.forEach(line => {
        breakdown[line.status] = (breakdown[line.status] || 0) + 1
      })
    })
    return breakdown
  }

  /**
   * 事件发射器 - 照搬1.4版本实现
   * @param {string} eventName - 事件名称
   * @param {*} data - 事件数据
   */
  emit(eventName, data) {
    const listeners = this.eventListeners.get(eventName)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`事件监听器执行失败 ${eventName}:`, error)
        }
      })
    }
  }

  /**
   * 添加事件监听器 - 照搬1.4版本实现
   * @param {string} eventName - 事件名称
   * @param {Function} listener - 监听器函数
   */
  on(eventName, listener) {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, [])
    }
    this.eventListeners.get(eventName).push(listener)
  }

  /**
   * 移除事件监听器 - 照搬1.4版本实现
   * @param {string} eventName - 事件名称
   * @param {Function} listener - 监听器函数
   */
  off(eventName, listener) {
    const listeners = this.eventListeners.get(eventName)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 应用电压等级筛选条件 - 从1.4版本复制
   * @param {Array} voltageFilters - 电压等级筛选条件
   * @param {Object} linesData - 线路数据
   */
  async applyVoltageFilters(voltageFilters, linesData) {
    this.activeVoltageFilters = voltageFilters
    console.log('应用电压等级筛选条件:', voltageFilters)

    // 重新渲染所有线路（会自动应用状态筛选）
    await this.renderAllLines(linesData, this.activeFilters)

    this.emit('voltageFiltersApplied', { voltageFilters, summary: this.getRenderSummary() })
  }

  /**
   * 应用线路级别筛选条件 - 从1.4版本复制
   * @param {Array} levelFilters - 线路级别筛选条件
   * @param {Object} linesData - 线路数据
   */
  async applyLevelFilters(levelFilters, linesData) {
    this.activeLevelFilters = levelFilters
    console.log('应用线路级别筛选条件:', levelFilters)

    // 重新渲染所有线路（会自动应用状态和电压等级筛选）
    await this.renderAllLines(linesData, this.activeFilters)

    this.emit('levelFiltersApplied', { levelFilters, summary: this.getRenderSummary() })
  }

  /**
   * 同时应用状态、电压等级和线路级别筛选条件 - 从1.4版本复制
   * @param {Array} statusFilters - 状态筛选条件
   * @param {Array} voltageFilters - 电压等级筛选条件
   * @param {Array} levelFilters - 线路级别筛选条件
   * @param {Object} linesData - 线路数据
   */
  async applyCombinedFilters(statusFilters, voltageFilters, levelFilters, linesData) {
    this.activeFilters = statusFilters
    this.activeVoltageFilters = voltageFilters
    this.activeLevelFilters = levelFilters || this.activeLevelFilters

    console.log('应用组合筛选条件:', {
      status: statusFilters,
      voltage: voltageFilters,
      level: this.activeLevelFilters
    })

    // 重新渲染所有线路
    await this.renderAllLines(linesData, statusFilters)

    this.emit('combinedFiltersApplied', {
      statusFilters,
      voltageFilters,
      levelFilters: this.activeLevelFilters,
      summary: this.getRenderSummary()
    })
  }

  /**
   * 根据线路坐标获取天气数据
   * @param {Array} coordinates - 线路坐标数组 [[lng, lat], [lng, lat]]
   * @returns {Object|null} 天气数据 {temperature, weather}
   */
  getWeatherDataForLine(coordinates) {
    if (!this.weatherManager || !coordinates || coordinates.length === 0) {
      return null
    }

    // 取线路中点坐标
    const midIndex = Math.floor(coordinates.length / 2)
    const [lng, lat] = coordinates[midIndex]

    // 查找最近的天气数据点
    let closestWeatherData = null
    let minDistance = Infinity

    // 遍历天气数据，找到最近的点
    for (const [place, weatherData] of this.weatherManager.weatherData) {
      // 从天气数据中提取坐标（如果有的话）
      const weatherCoords = this.getCoordinatesForPlace(place)
      if (weatherCoords) {
        const distance = this.calculateDistance(lng, lat, weatherCoords[0], weatherCoords[1])
        if (distance < minDistance) {
          minDistance = distance
          closestWeatherData = {
            temperature: weatherData.temperature ? `${weatherData.temperature}°C` : null,
            weather: weatherData.weather || weatherData.weather1 || null,
            precipitation: weatherData.precipitation || 0, // 添加降水量字段
            place: place,
            distance: Math.round(distance)
          }
        }
      }
    }

    return closestWeatherData
  }

  /**
   * 获取地区对应的坐标
   * @param {string} place - 地区名称
   * @returns {Array|null} 坐标 [lng, lat]
   */
  getCoordinatesForPlace(place) {
    // 主要城市坐标映射
    const cityCoordinates = {
      '昆明': [102.712251, 25.040609],
      '大理': [100.267638, 25.606486],
      '丽江': [100.233026, 26.872108],
      '香格里拉': [99.706463, 27.826853],
      '西双版纳': [100.797941, 22.009433],
      '曲靖': [103.797851, 25.501557],
      '玉溪': [102.543907, 24.350461],
      '保山': [99.167133, 25.111802],
      '昭通': [103.717216, 27.336999],
      '普洱': [100.972344, 22.777321],
      '临沧': [100.08697, 23.886567],
      '楚雄': [101.546046, 25.041988],
      '红河': [103.384182, 23.366775],
      '文山': [104.24401, 23.36951],
      '德宏': [98.578363, 24.436694],
      '怒江': [98.854304, 25.850949],
      '迪庆': [99.706463, 27.826853]
    }

    // 直接匹配
    if (cityCoordinates[place]) {
      return cityCoordinates[place]
    }

    // 模糊匹配（去掉市、州等后缀）
    const simpleName = place.replace(/[市州区县]/g, '')
    if (cityCoordinates[simpleName]) {
      return cityCoordinates[simpleName]
    }

    return null
  }

  /**
   * 计算两点间距离（简化版，单位：公里）
   * @param {number} lng1 - 经度1
   * @param {number} lat1 - 纬度1
   * @param {number} lng2 - 经度2
   * @param {number} lat2 - 纬度2
   * @returns {number} 距离（公里）
   */
  calculateDistance(lng1, lat1, lng2, lat2) {
    const R = 6371 // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }

  /**
   * 销毁渲染器 - 照搬1.4版本实现
   */
  destroy() {
    this.clearAllLines()
    this.eventListeners.clear()
    console.log('PowerLineRenderer 已销毁')
  }
}
