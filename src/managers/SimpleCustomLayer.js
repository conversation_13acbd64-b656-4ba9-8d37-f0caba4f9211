import * as THREE from 'three'

/**
 * 基于THREE.js的3D自定义图层管理器
 * 参考高德地图官方示例和最佳实践重新实现
 * 解决WebGL上下文状态管理和相机参数同步问题
 */

class SimpleCustomLayer {
  constructor(options = {}) {
    this.options = {
      center: [102.712251, 25.040609], // 昆明市中心坐标
      ...options
    }

    this.map = null
    this.customCoords = null
    this.glLayer = null
    this.container = null // 地图容器引用
    this.isInitialized = false
    this.isVisible = true

    // THREE.js相关
    this.renderer = null
    this.scene = null
    this.camera = null
    this.cube = null
    this.animationTime = 0

    // 绑定方法上下文
    this.render = this.render.bind(this)
  }

  /**
   * 初始化图层
   */
  async init(map) {
    try {
      console.log('开始初始化3D自定义图层...')

      if (!map) {
        throw new Error('地图实例不能为空')
      }

      this.map = map

      // 获取地图容器引用
      this.container = this.map.getContainer()
      if (!this.container) {
        console.error('无法获取地图容器')
        return false
      }

      // 获取自定义坐标系统
      this.customCoords = this.map.customCoords
      if (!this.customCoords) {
        console.error('无法获取地图的customCoords对象')
        return false
      }

      // 创建GLCustomLayer
      this.glLayer = new AMap.GLCustomLayer({
        zIndex: 10,
        visible: true, // 确保图层可见
        init: (gl) => {
          console.log('GLCustomLayer init 开始')
          this.initTHREE(gl)
          console.log('GLCustomLayer init 完成')
        },
        render: () => {
          this.render()
        }
      })

      // 添加到地图
      this.map.add(this.glLayer)

      // 添加窗口大小变化监听
      window.addEventListener('resize', this.onWindowResize.bind(this))

      this.isInitialized = true

      console.log('3D自定义图层初始化成功')
      return true
    } catch (error) {
      console.error('3D自定义图层初始化失败:', error)
      return false
    }
  }

  /**
   * 初始化THREE.js
   * 参考最佳实践重新实现
   */
  initTHREE(gl) {
    try {
      // 重要：初始化高德地图的自定义坐标系统
      this.customCoords = new AMap.CustomCoords(this.map, this.options.center)
      console.log('自定义坐标系统已初始化，中心点:', this.options.center)

      // 创建THREE.js渲染器，使用地图的WebGL上下文
      this.renderer = new THREE.WebGLRenderer({
        context: gl, // 使用地图的WebGL上下文
        alpha: true,
        antialias: false, // 关闭抗锯齿以提高性能
        precision: 'highp'
      })

      // 重要：禁用自动清除，让地图背景显示
      this.renderer.autoClear = false
      this.renderer.sortObjects = false
      this.renderer.setClearAlpha(0)

      // 创建场景
      this.scene = new THREE.Scene()

      // 获取地图容器尺寸并设置渲染器和相机
      const { clientWidth, clientHeight } = this.container
      this.renderer.setSize(clientWidth, clientHeight)
      this.renderer.setPixelRatio(window.devicePixelRatio)

      // 创建相机 - 参数会在render中动态更新
      this.camera = new THREE.PerspectiveCamera(
        60, // fov
        clientWidth / clientHeight, // aspect - 使用容器尺寸
        100, // near
        1 << 30 // far
      )

      // 添加光照
      this.addLights()

      // 创建立方体
      this.createCube()

      console.log('THREE.js初始化完成')
    } catch (error) {
      console.error('THREE.js初始化失败:', error)
    }
  }

  /**
   * 添加光照
   */
  addLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
    this.scene.add(ambientLight)

    // 平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(1000, -100, 900)
    this.scene.add(directionalLight)
  }

  /**
   * 创建立方体
   */
  createCube() {
    try {
      console.log('开始创建立方体，中心点:', this.options.center)
      console.log('地图容器尺寸:', {
        width: this.container.clientWidth,
        height: this.container.clientHeight
      })

      // 创建合适大小的立方体，放置在昆明位置
      const cubeSize = 20000 // 20公里的立方体
      const geometry = new THREE.BoxGeometry(cubeSize, cubeSize, cubeSize)

      // 使用发光材质，确保立方体容易被看到
      const material = new THREE.MeshBasicMaterial({
        color: 0x00ff00, // 亮绿色
        transparent: true,
        opacity: 0.8,
        wireframe: false,
        side: THREE.DoubleSide,
        emissive: 0x004400 // 添加发光效果
      })

      // 创建主立方体 - 在GLCustomLayer中，(0,0,0)对应设置的中心点(昆明)
      this.cube = new THREE.Mesh(geometry, material)
      this.cube.position.set(0, 0, 10000) // 放在昆明上方10公里处
      this.scene.add(this.cube)
      console.log('主立方体已创建，位置: (0, 0, 10000) - 对应昆明上方10公里')

      // 创建几个测试立方体，用于验证坐标系
      // 注意：在高德地图的坐标系中，X轴指向东，Y轴指向北，Z轴指向上
      const testCubes = [
        { pos: [0, 0, 5000], color: 0xff0000, size: 10000, name: '红色-中心低' }, // 红色，较低高度
        { pos: [10000, 0, 8000], color: 0x0000ff, size: 8000, name: '蓝色-东' }, // 蓝色，东偏移
        { pos: [-10000, 0, 8000], color: 0xffff00, size: 8000, name: '黄色-西' }, // 黄色，西偏移
        { pos: [0, 10000, 8000], color: 0xff00ff, size: 8000, name: '紫色-北' }, // 紫色，北偏移
        { pos: [0, -10000, 8000], color: 0x00ffff, size: 8000, name: '青色-南' } // 青色，南偏移
      ]

      testCubes.forEach((cubeData, index) => {
        const testGeometry = new THREE.BoxGeometry(cubeData.size, cubeData.size, cubeData.size)
        const testMaterial = new THREE.MeshBasicMaterial({
          color: cubeData.color,
          transparent: true,
          opacity: 0.7,
          wireframe: false,
          side: THREE.DoubleSide,
          emissive: cubeData.color * 0.3 // 添加发光效果
        })

        const testCube = new THREE.Mesh(testGeometry, testMaterial)
        testCube.position.set(...cubeData.pos)
        this.scene.add(testCube)
        console.log(`测试立方体${index + 1}(${cubeData.name})已创建，位置:`, ...cubeData.pos)
      })

      // 添加坐标轴辅助器 - 标准地图坐标系
      // 红色X轴指向东，绿色Y轴指向北，蓝色Z轴指向上
      const axesHelper = new THREE.AxesHelper(30000)
      this.scene.add(axesHelper)
      console.log('坐标轴辅助器已添加，尺寸: 30000')
      console.log('坐标系说明: 红色X轴=东, 绿色Y轴=北, 蓝色Z轴=上')

    } catch (error) {
      console.error('创建立方体失败:', error)
    }
  }

  /**
   * 渲染函数 - 每帧调用
   * 参考最佳实践重新实现，解决WebGL状态管理问题
   */
  render() {
    if (!this.renderer || !this.scene || !this.camera || !this.customCoords) {
      return
    }

    try {
      // 重要：在渲染开始前重置WebGL状态
      this.renderer.resetState()

      // 重新设置渲染中心点，确保与其他图层兼容
      this.customCoords.setCenter(this.options.center)

      // 每1000帧输出一次中心点信息用于调试
      if (this.animationTime % 1000 === 0) {
        console.log('渲染中心点 (昆明坐标):', this.options.center)
      }

      // 获取相机参数
      const cameraParams = this.customCoords.getCameraParams()
      if (!cameraParams) {
        console.warn('无法获取相机参数')
        return
      }

      const { near, far, fov, up, lookAt, position } = cameraParams

      // 按照正确的顺序同步相机参数
      this.camera.near = near
      this.camera.far = far
      this.camera.fov = fov
      this.camera.position.set(...position)
      this.camera.up.set(...up)
      this.camera.lookAt(...lookAt)
      this.camera.updateProjectionMatrix()

      // 添加旋转动画
      if (this.cube) {
        this.cube.rotation.x += 0.01
        this.cube.rotation.y += 0.01

        // 每500帧输出一次调试信息
        if (this.animationTime % 500 === 0) {
          console.log('=== 3D图层调试信息 ===')
          console.log('立方体状态:', {
            position: this.cube.position,
            rotation: this.cube.rotation,
            visible: this.cube.visible,
            scale: this.cube.scale
          })
          console.log('相机状态:', {
            position: this.camera.position,
            lookAt: lookAt,
            fov: this.camera.fov,
            aspect: this.camera.aspect,
            near: this.camera.near,
            far: this.camera.far
          })
          console.log('容器尺寸:', {
            width: this.container.clientWidth,
            height: this.container.clientHeight
          })
          console.log('渲染器尺寸:', {
            width: this.renderer.domElement.width,
            height: this.renderer.domElement.height
          })
          console.log('渲染中心:', this.options.center)
          console.log('========================')
        }
      }

      // 更新相机宽高比 - 不需要重复设置渲染器尺寸
      const { clientWidth, clientHeight } = this.container
      this.camera.aspect = clientWidth / clientHeight
      this.camera.updateProjectionMatrix()

      // 渲染场景
      this.renderer.render(this.scene, this.camera)

      // 重要：渲染完成后重置WebGL状态
      this.renderer.resetState()

      this.animationTime++

    } catch (error) {
      console.error('渲染错误:', error)
    }
  }



  /**
   * 更新渲染中心点
   */
  updateCenter(lngLat) {
    if (lngLat instanceof Array && lngLat.length === 2) {
      this.options.center = lngLat
      if (this.customCoords) {
        this.customCoords.setCenter(lngLat)
      }
    }
  }

  /**
   * 窗口大小变化处理
   */
  onWindowResize() {
    if (this.camera && this.container && this.renderer) {
      const { clientWidth, clientHeight } = this.container
      this.camera.aspect = clientWidth / clientHeight
      this.camera.updateProjectionMatrix()

      // 重新设置渲染器尺寸
      this.renderer.setSize(clientWidth, clientHeight)

      console.log('窗口大小变化，更新渲染器尺寸:', { clientWidth, clientHeight })
    }
  }

  /**
   * 显示图层
   */
  show() {
    this.isVisible = true
    if (this.glLayer) {
      this.glLayer.show()
    }
  }

  /**
   * 隐藏图层
   */
  hide() {
    this.isVisible = false
    if (this.glLayer) {
      this.glLayer.hide()
    }
  }

  /**
   * 销毁图层
   */
  destroy() {
    try {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', this.onWindowResize)

      // 从地图中移除图层
      if (this.glLayer && this.map) {
        this.map.remove(this.glLayer)
      }

      // 清理THREE.js资源
      if (this.scene) {
        // 清理场景中的对象
        while (this.scene.children.length > 0) {
          const child = this.scene.children[0]
          this.scene.remove(child)
          if (child.geometry) child.geometry.dispose()
          if (child.material) {
            if (child.material.map) child.material.map.dispose()
            child.material.dispose()
          }
        }
      }

      // 清理渲染器
      if (this.renderer) {
        this.renderer.dispose()
      }

      // 重置状态
      this.renderer = null
      this.scene = null
      this.camera = null
      this.cube = null
      this.glLayer = null
      this.customCoords = null
      this.isInitialized = false

      console.log('3D自定义图层已销毁')
    } catch (error) {
      console.error('销毁图层时出错:', error)
    }
  }

  /**
   * 获取图层状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isVisible: this.isVisible,
      hasRenderer: !!this.renderer,
      hasScene: !!this.scene,
      hasCube: !!this.cube,
      hasCustomCoords: !!this.customCoords,
      center: this.options.center
    }
  }
}

export default SimpleCustomLayer
