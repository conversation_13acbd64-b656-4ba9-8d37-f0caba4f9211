/**
 * 3D模型管理器
 * 在3D模式下动态加载和卸载电塔3D模型
 * 实现视野范围内的性能优化加载
 */

import { MAP_CONFIG, ZOOM_THRESHOLDS, PERFORMANCE_CONFIG } from '../data/powerConfig.js'
import { extractTowerPositions, getVisibleTowers, isPointInBounds } from '../data/index.js'

export class Model3DManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap

    // 已加载的3D模型存储
    this.loadedModels = new Map() // 模型ID -> 模型对象
    this.visibleModels = new Map() // 当前可见的3D模型
    this.allTowers = [] // 所有电塔位置数据

    // 3D模型资源路径 - 修正为相对于public目录的路径
    this.modelPath = './assets/model/dt.gltf'

    // 当前视野范围
    this.visibleBounds = null

    // 加载状态
    this.isLoading = false
    this.loadError = null
    this.hasSuccessfullyLoadedModels = false // 新增：是否成功加载过3D模型
    this.isUpdatingModels = false // 防止并发更新模型
    this.isCreatingModels = false // 防止并发创建模型

    // 性能配置
    this.maxModels = PERFORMANCE_CONFIG.MAX_VISIBLE_MODELS
    this.loadBatchSize = PERFORMANCE_CONFIG.LOAD_BATCH_SIZE
    this.debounceDelay = PERFORMANCE_CONFIG.DEBOUNCE_DELAY
    this.minZoom = ZOOM_THRESHOLDS.SHOW_3D_MODELS

    // 对象池
    this.modelPool = []
    this.maxPoolSize = PERFORMANCE_CONFIG.MAX_POOL_SIZE

    // 防抖定时器
    this.modelManageTimer = null

    // 性能监控定时器
    this.performanceMonitorTimer = null

    // 海量点管理器引用（用于检查筛选状态）
    this.massMarkerManager = null

    // 事件监听器
    this.eventListeners = new Map()

    // 3D对象图层
    this.object3DLayer = null

    // 外部管理器引用（用于通知2D图标管理器）
    this.massMarkerManager = null

    // 缩放和位置跟踪（用于防止重复加载）
    this.lastZoom = 0
    this.lastCenter = null
    this.lastBoundsKey = null
    this.zoomChangeTimer = null

    // 监听地图事件
    this.setupMapListeners()

    console.log('Model3DManager 初始化完成')

    // 添加全局调试方法
    if (typeof window !== 'undefined') {
      window.debug3DModels = () => this.debugModelStatus()
      window.cleanup3DModels = () => this.cleanupDuplicateModels()
      window.force3DReload = () => this.forceReload3DModels()
      console.log('💡 调试提示：')
      console.log('  - debug3DModels() 查看模型状态')
      console.log('  - cleanup3DModels() 清理重复模型')
      console.log('  - force3DReload() 强制重新加载所有模型')
    }
  }

  /**
   * 设置外部管理器引用
   * @param {Object} massMarkerManager - 海量点管理器实例
   */
  setMassMarkerManager(massMarkerManager) {
    this.massMarkerManager = massMarkerManager
  }

  /**
   * 设置地图事件监听
   */
  setupMapListeners() {
    console.log('设置地图事件监听...')

    // 监听地图缩放变化
    this.map.on('zoomchange', () => {
      console.log('🔍 缩放变化事件触发')
      this.handleZoomChange()
    })

    // 监听地图移动 - 使用多个事件确保覆盖所有移动情况
    this.map.on('mapmove', () => {
      console.log('🚀 地图移动事件触发 (mapmove)')
      this.handleMapMove()
    })

    // 监听地图拖拽结束
    this.map.on('dragend', () => {
      console.log('🚀 地图拖拽结束事件触发 (dragend)')
      this.handleMapMove()
    })

    // 监听地图移动结束
    this.map.on('moveend', () => {
      console.log('🚀 地图移动结束事件触发 (moveend)')
      this.handleMapMove()
    })

    // 监听地图视野变化
    this.map.on('complete', () => {
      console.log('🚀 地图加载完成事件触发 (complete)')
      // 延迟一点执行，确保地图完全加载
      setTimeout(() => {
        if (this.shouldLoad3DModels()) {
          this.manage3DModels()
        }
      }, 500)
    })

    console.log('地图事件监听设置完成')
  }

  /**
   * 测试地图事件触发
   */
  testMapEvents() {
    console.log('=== 测试地图事件触发 ===')
    console.log('当前地图中心:', this.map.getCenter())
    console.log('当前缩放级别:', this.map.getZoom())
    console.log('当前视图模式:', this.getViewMode())
    console.log('电塔数据数量:', this.allTowers.length)
    console.log('可见模型数量:', this.visibleModels.size)

    // 手动触发一次智能更新
    if (this.shouldLoad3DModels()) {
      console.log('手动触发智能更新...')
      this.smartUpdateModels()
    } else {
      console.log('不满足3D模型显示条件')
    }
    console.log('=== 测试完成 ===')
  }

  /**
   * 强制刷新3D模型显示
   * 用于解决地图移动后模型不显示的问题
   */
  forceRefresh3DModels() {
    console.log('=== 强制刷新3D模型显示 ===')

    if (!this.shouldLoad3DModels()) {
      console.log('不满足3D模型显示条件，跳过刷新')
      return
    }

    // 清除当前所有模型
    console.log('清除当前所有模型...')
    this.visibleModels.forEach((modelData) => {
      if (modelData.model && this.object3DLayer) {
        this.object3DLayer.remove(modelData.model)
        this.returnModelToPool(modelData.model)
      }
    })
    this.visibleModels.clear()

    // 重新加载电塔数据
    console.log('重新加载电塔数据...')
    this.loadTowerPositions()

    // 清理可能的重复模型
    console.log('清理重复模型...')
    this.cleanupDuplicateModels()

    // 强制触发智能更新
    console.log('强制触发智能更新...')
    this.smartUpdateModels().then(() => {
      console.log('强制刷新完成，当前可见模型数量:', this.visibleModels.size)
    }).catch(error => {
      console.error('强制刷新失败:', error)
    })

    console.log('=== 强制刷新触发完成 ===')
  }

  /**
   * 初始化3D对象图层
   */
  init3DLayer() {
    try {
      // 检查高德地图版本和3D支持
      console.log('=== 3D图层初始化开始 ===')
      console.log('AMap版本:', this.AMap.version)
      console.log('GltfLoader可用性:', !!this.AMap.GltfLoader)
      console.log('Object3DLayer可用性:', !!this.AMap.Object3DLayer)
      console.log('可用的3D相关API:', Object.keys(this.AMap).filter(key => key.includes('3D') || key.includes('Object') || key.includes('Gltf')))

      // 高德地图1.4.9版本使用Object3DLayer
      if (this.AMap.Object3DLayer) {
        this.object3DLayer = new this.AMap.Object3DLayer()
        this.map.add(this.object3DLayer)
        console.log('✓ 3D对象图层初始化成功')
        console.log('✓ GltfLoader插件可用:', !!this.AMap.GltfLoader)
        console.log('✓ 模型路径:', this.modelPath)
        console.log('=== 3D图层初始化完成 ===')
        return true
      }
      // 尝试其他可能的3D图层API
      else if (this.AMap.GLCustomLayer) {
        console.log('尝试使用GLCustomLayer')
        // 这是另一种可能的3D图层实现方式
        return this.initGLCustomLayer()
      }
      else {
        console.warn('✗ 当前AMap版本不支持3D对象图层')
        console.log('AMap对象属性:', Object.keys(this.AMap))
        console.log('=== 3D图层初始化失败 ===')
        return false
      }
    } catch (error) {
      console.error('✗ 3D对象图层初始化失败:', error)
      console.log('=== 3D图层初始化异常 ===')
      return false
    }
  }

  /**
   * 初始化GL自定义图层（备用方案）
   */
  initGLCustomLayer() {
    try {
      // 这里可以实现GLCustomLayer的初始化
      console.log('GLCustomLayer初始化暂未实现')
      return false
    } catch (error) {
      console.error('GLCustomLayer初始化失败:', error)
      return false
    }
  }

  /**
   * 检查是否应该加载3D模型
   * @returns {boolean} 是否应该加载
   */
  shouldLoad3DModels() {
    const currentZoom = this.map.getZoom()
    const viewMode = this.getViewMode()

    // 检查基本条件
    const basicConditions = viewMode === '3D' && currentZoom >= this.minZoom && this.object3DLayer

    // 检查筛选状态
    const towerFiltersEnabled = this.massMarkerManager ? this.massMarkerManager.towerFiltersEnabled : true

    // 调试日志
    console.log('3D模型加载条件检查:')
    console.log('- 视图模式:', viewMode, '(需要3D)')
    console.log('- 当前缩放:', currentZoom, '(需要>=', this.minZoom, ')')
    console.log('- 3D图层存在:', !!this.object3DLayer)
    console.log('- 电塔筛选启用:', towerFiltersEnabled)
    console.log('- 基本条件满足:', basicConditions)
    console.log('- 最终结果:', basicConditions && towerFiltersEnabled)

    // 只有在基本条件满足且电塔筛选启用时才加载3D模型
    return basicConditions && towerFiltersEnabled
  }

  /**
   * 获取地图视图模式
   * @returns {string} 视图模式
   */
  getViewMode() {
    try {
      if (typeof this.map.getViewMode === 'function') {
        return this.map.getViewMode()
      } else if (typeof this.map.getViewMode_ === 'function') {
        return this.map.getViewMode_()
      } else {
        // 通过其他方式判断是否为3D模式
        const pitch = this.map.getPitch ? this.map.getPitch() : 0
        return pitch > 0 ? '3D' : '2D'
      }
    } catch (error) {
      console.warn('获取视图模式失败:', error)
      return '2D'
    }
  }

  /**
   * 加载电塔位置数据
   */
  loadTowerPositions() {
    try {
      console.log('开始加载电塔位置数据...')
      this.allTowers = extractTowerPositions()
      console.log(`电塔位置数据加载完成: ${this.allTowers.length} 个位置`)

      if (this.allTowers.length > 0) {
        console.log('电塔数据示例:', this.allTowers.slice(0, 3))

        // 调试：检查是否有重复位置的电塔
        const positionMap = new Map()
        const duplicates = []

        this.allTowers.forEach(tower => {
          const positionKey = `${tower.position[0]},${tower.position[1]}`
          if (positionMap.has(positionKey)) {
            duplicates.push({
              position: positionKey,
              towers: [positionMap.get(positionKey), tower]
            })
          } else {
            positionMap.set(positionKey, tower)
          }
        })

        if (duplicates.length > 0) {
          console.warn(`⚠️ 发现 ${duplicates.length} 个重复位置的电塔:`)
          duplicates.slice(0, 3).forEach(dup => {
            console.warn(`位置 ${dup.position}:`, dup.towers.map(t => ({id: t.id, name: t.name})))
          })
        } else {
          console.log('✅ 电塔位置数据已去重，无重复位置')
        }
      } else {
        console.warn('⚠️ 没有找到电塔数据')
      }
    } catch (error) {
      console.error('加载电塔位置数据失败:', error)
      this.allTowers = []
    }
  }

  /**
   * 更新电塔位置数据
   * @param {Array} linesData - 线路数据（包含电塔位置）
   */
  updateTowerPositions(linesData) {
    // 使用数据提取函数获取电塔位置
    this.loadTowerPositions()

    // 如果当前应该显示3D模型，则管理模型显示
    if (this.shouldLoad3DModels()) {
      console.log('满足3D模型显示条件，开始管理模型')
      this.manage3DModels()
    } else {
      console.log('不满足3D模型显示条件')
      console.log('- 当前缩放:', this.map.getZoom())
      console.log('- 最小缩放:', this.minZoom)
      console.log('- 视图模式:', this.getViewMode())
      console.log('- 3D图层:', !!this.object3DLayer)
    }
  }

  /**
   * 管理3D模型显示
   * 根据缩放级别和视野范围动态加载/卸载模型
   */
  manage3DModels() {
    // 清除之前的定时器
    if (this.modelManageTimer) {
      clearTimeout(this.modelManageTimer)
    }

    // 使用防抖，避免频繁调用
    this.modelManageTimer = setTimeout(async () => {
      if (this.isLoading || this.isUpdatingModels || this.isCreatingModels) {
        console.log('🔄 其他操作正在进行中，跳过模型管理')
        return
      }

      try {
        const currentZoom = this.map.getZoom()
        const viewMode = this.getViewMode()

        // 输出详细的状态信息
        this.logModelStatus('manage3DModels 开始')
        console.log(`🎯 3D模型管理: 缩放=${currentZoom}, 视图模式=${viewMode}, 最小缩放=${this.minZoom}`)

        // 检查是否需要显示3D模型
        if (currentZoom < this.minZoom) {
          // 隐藏所有3D模型
          console.log('📏 缩放级别不足，隐藏3D模型')
          this.hide3DModels()
          return
        }

        // 检查是否为3D模式
        if (viewMode !== '3D') {
          console.log('🎭 非3D模式，隐藏3D模型')
          this.hide3DModels()
          return
        }

        // 检查是否有3D图层
        if (!this.object3DLayer) {
          console.log('🚫 3D图层未初始化，跳过模型加载')
          return
        }

        // 检查筛选状态
        const towerFiltersEnabled = this.massMarkerManager ? this.massMarkerManager.towerFiltersEnabled : true
        if (!towerFiltersEnabled) {
          console.log('🔍 电塔筛选已禁用，隐藏3D模型')
          this.hide3DModels()
          return
        }

        // 显示3D模型
        console.log('✅ 条件满足，开始显示3D模型')
        await this.show3DModels()

        // 输出最终状态
        this.logModelStatus('manage3DModels 完成')
      } catch (error) {
        console.error('❌ 管理3D模型显示失败:', error)
        this.isLoading = false // 确保重置加载状态
      }
    }, this.debounceDelay)
  }

  /**
   * 输出模型状态日志
   * @param {string} context - 上下文信息
   */
  logModelStatus(context) {
    console.log(`📊 [${context}] 模型状态统计:`)
    console.log(`- 可见模型数量: ${this.visibleModels.size}`)
    console.log(`- 对象池大小: ${this.modelPool.length}/${this.maxPoolSize}`)
    console.log(`- 总电塔数量: ${this.allTowers.length}`)
    console.log(`- 是否正在加载: ${this.isLoading}`)
    console.log(`- 是否成功加载过: ${this.hasSuccessfullyLoadedModels}`)
    console.log(`- 筛选状态: ${this.massMarkerManager ? this.massMarkerManager.towerFiltersEnabled : 'unknown'}`)
  }

  /**
   * 显示3D模型（支持增量更新）
   */
  async show3DModels() {
    if (this.isLoading) return

    this.isLoading = true

    try {
      // 检查是否已有模型显示，如果有则使用智能更新
      if (this.visibleModels.size > 0) {
        console.log('已有模型显示，使用智能更新模式')
        await this.smartUpdateModels()
        return
      }

      // 首次加载或完全重新加载
      console.log('首次加载3D模型')
      await this.initialLoadModels()

    } catch (error) {
      console.error('显示3D模型失败:', error)
      this.hasSuccessfullyLoadedModels = false
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 初始加载3D模型
   */
  async initialLoadModels() {
    let successfullyLoadedCount = 0

    try {
      // 获取视窗内的电塔
      let visibleTowers = this.getVisibleTowersInView()

      console.log(`视野内发现 ${visibleTowers.length} 个电塔位置`)

      // 性能优化：限制最大显示数量
      if (visibleTowers.length > this.maxModels) {
        // 按电压等级和重要性排序，优先显示重要的电塔
        visibleTowers = visibleTowers
          .sort((a, b) => {
            // 电压等级优先（500KV > 220KV > 110KV > 35KV）
            const voltageOrder = { '500KV': 4, '220KV': 3, '110KV': 2, '35KV': 1, '10KV': 0 }
            const aVoltage = a.voltage || '500KV'
            const bVoltage = b.voltage || '500KV'
            return (voltageOrder[bVoltage] || 0) - (voltageOrder[aVoltage] || 0)
          })
          .slice(0, this.maxModels)

        console.log(`限制显示数量为 ${this.maxModels} 个电塔`)
      }

      // 使用统一的模型添加方法
      await this.addModelsForTowers(visibleTowers)
      successfullyLoadedCount = this.visibleModels.size

      // 更新成功加载状态
      this.hasSuccessfullyLoadedModels = successfullyLoadedCount > 0

      console.log(`初始加载完成，成功显示了 ${successfullyLoadedCount} 个3D模型`)

      // 如果没有成功加载任何模型，输出详细的调试信息
      if (successfullyLoadedCount === 0 && visibleTowers.length > 0) {
        console.warn('=== 3D模型加载失败诊断 ===')
        console.warn('视野内电塔数量:', visibleTowers.length)
        console.warn('模型路径:', this.modelPath)
        console.warn('AMap.GltfLoader可用性:', !!this.AMap.GltfLoader)
        console.warn('Object3DLayer可用性:', !!this.object3DLayer)
        console.warn('地图缩放级别:', this.map.getZoom())
        console.warn('最小显示缩放级别:', this.minZoom)
        console.warn('=== 诊断结束 ===')
      }

      // 通知海量点管理器更新2D图标显示状态
      this.notifyMassMarkerManager()

    } catch (error) {
      console.error('初始加载3D模型失败:', error)
      this.hasSuccessfullyLoadedModels = false
    }
  }

  /**
   * 隐藏3D模型
   */
  hide3DModels() {
    try {
      console.log(`🔄 开始隐藏3D模型，当前可见模型数量: ${this.visibleModels.size}`)

      let removedCount = 0
      let pooledCount = 0
      let errorCount = 0

      // 隐藏所有3D模型并回收到对象池
      this.visibleModels.forEach((modelData, modelId) => {
        try {
          if (modelData.model && this.object3DLayer) {
            // 从3D图层中移除模型
            this.object3DLayer.remove(modelData.model)
            removedCount++

            // 将模型返回到对象池
            this.returnModelToPool(modelData.model)
            pooledCount++

            console.log(`✅ 模型已隐藏并回收: ${modelId}`)
          } else {
            console.warn(`⚠️ 模型数据不完整，跳过: ${modelId}`)
          }
        } catch (error) {
          console.error(`❌ 隐藏模型失败 ${modelId}:`, error)
          errorCount++
        }
      })

      // 清空可见模型记录
      this.visibleModels.clear()

      // 更新加载状态
      this.hasSuccessfullyLoadedModels = false

      // 通知海量点管理器更新2D图标显示状态
      this.notifyMassMarkerManager()

      console.log(`✅ 3D模型隐藏完成: 移除${removedCount}个，回收${pooledCount}个，错误${errorCount}个`)
      console.log(`📊 当前对象池大小: ${this.modelPool.length}`)
    } catch (error) {
      console.error('❌ 隐藏3D模型失败:', error)
    }
  }

  /**
   * 通知海量点管理器更新显示状态
   */
  notifyMassMarkerManager() {
    if (this.massMarkerManager && typeof this.massMarkerManager.controlTowerMarkersVisibility === 'function') {
      const currentZoom = this.map.getZoom()
      const viewMode = this.getViewMode()

      // 传递3D模型加载状态给海量点管理器
      this.massMarkerManager.controlTowerMarkersVisibility(
        currentZoom,
        viewMode,
        this.hasSuccessfullyLoadedModels
      )
    }
  }

  /**
   * 获取视野范围内的电塔
   * @returns {Array} 视野范围内的电塔数组
   */
  getVisibleTowersInView() {
    try {
      console.log('=== 开始视野范围检查 ===')
      console.log('总电塔数量:', this.allTowers.length)
      console.log('地图中心:', this.map.getCenter())
      console.log('地图缩放:', this.map.getZoom())

      // 检查电塔数据是否存在
      if (!this.allTowers || this.allTowers.length === 0) {
        console.warn('⚠️ 没有电塔数据，尝试重新加载...')
        this.loadTowerPositions()
        if (!this.allTowers || this.allTowers.length === 0) {
          console.warn('⚠️ 重新加载后仍然没有电塔数据')
          return []
        }
      }

      // 优先使用备用方案（基于中心点和距离），因为它更可靠
      const centerBasedTowers = this.getVisibleTowersByCenter()

      // 尝试获取地图边界作为验证
      let boundsBasedTowers = []
      try {
        const bounds = this.map.getBounds()
        if (bounds) {
          boundsBasedTowers = this.getVisibleTowersWithImprovedBounds(bounds)
        }
      } catch (boundsError) {
        console.warn('获取地图边界失败，使用中心点方案:', boundsError.message)
      }

      // 选择结果更多的方案，但优先使用中心点方案
      let visibleTowers = centerBasedTowers
      if (boundsBasedTowers.length > centerBasedTowers.length * 1.2) {
        // 只有当边界方案的结果明显更多时才使用
        visibleTowers = boundsBasedTowers
        console.log('使用边界检测方案')
      } else {
        console.log('使用中心点检测方案')
      }

      console.log(`📍 从 ${this.allTowers.length} 个电塔中筛选出 ${visibleTowers.length} 个视野内的电塔`)

      if (visibleTowers.length > 0) {
        console.log('✅ 视野内电塔示例:', visibleTowers.slice(0, 3).map(t => ({
          id: t.id,
          name: t.name,
          position: t.position,
          voltage: t.voltage
        })))
      } else {
        console.warn('⚠️ 视野内没有找到任何电塔')
        // 最后的备用方案：返回距离最近的几个电塔
        const nearestTowers = this.getNearestTowers(5)
        console.log(`最终备用方案：返回最近的 ${nearestTowers.length} 个电塔`)
        visibleTowers = nearestTowers
      }

      console.log('=== 视野范围检查完成 ===')
      return visibleTowers
    } catch (error) {
      console.error('获取视野内电塔失败:', error)
      console.error('错误堆栈:', error.stack)
      // 使用最终备用方案
      return this.getNearestTowers(5)
    }
  }

  /**
   * 使用改进的边界检测逻辑获取可见电塔
   * @param {Object} bounds - 地图边界对象
   * @returns {Array} 视野范围内的电塔数组
   */
  getVisibleTowersWithImprovedBounds(bounds) {
    try {
      let swLng, swLat, neLng, neLat

      // 尝试多种方式解析边界信息
      if (bounds.getSouthWest && bounds.getNorthEast) {
        // 标准高德地图Bounds对象
        const southwest = bounds.getSouthWest()
        const northeast = bounds.getNorthEast()
        swLng = southwest.lng || southwest.getLng()
        swLat = southwest.lat || southwest.getLat()
        neLng = northeast.lng || northeast.getLng()
        neLat = northeast.lat || northeast.getLat()

        console.log('使用标准Bounds对象解析边界')
      } else if (bounds.southwest && bounds.northeast) {
        // 自定义边界对象格式
        const southwest = bounds.southwest
        const northeast = bounds.northeast
        swLng = southwest.lng || southwest.getLng()
        swLat = southwest.lat || southwest.getLat()
        neLng = northeast.lng || northeast.getLng()
        neLat = northeast.lat || northeast.getLat()

        console.log('使用自定义边界对象解析边界')
      } else if (bounds._southWest && bounds._northEast) {
        // 私有属性格式
        const southwest = bounds._southWest
        const northeast = bounds._northEast
        swLng = southwest.lng || southwest.getLng()
        swLat = southwest.lat || southwest.getLat()
        neLng = northeast.lng || northeast.getLng()
        neLat = northeast.lat || northeast.getLat()

        console.log('使用私有属性解析边界')
      } else if (bounds.CLASS_NAME === 'AMap.ArrayBounds' && bounds.bounds && bounds.bounds.length >= 4) {
        // AMap.ArrayBounds 格式处理
        const boundsArray = bounds.bounds
        let minLng = Infinity, minLat = Infinity, maxLng = -Infinity, maxLat = -Infinity

        boundsArray.forEach(point => {
          let lng, lat
          if (point.lng !== undefined && point.lat !== undefined) {
            lng = point.lng
            lat = point.lat
          } else if (point.getLng && point.getLat) {
            lng = point.getLng()
            lat = point.getLat()
          } else if (Array.isArray(point) && point.length >= 2) {
            lng = point[0]
            lat = point[1]
          } else {
            return
          }

          minLng = Math.min(minLng, lng)
          minLat = Math.min(minLat, lat)
          maxLng = Math.max(maxLng, lng)
          maxLat = Math.max(maxLat, lat)
        })

        if (minLng !== Infinity && minLat !== Infinity && maxLng !== -Infinity && maxLat !== -Infinity) {
          swLng = minLng
          swLat = minLat
          neLng = maxLng
          neLat = maxLat
          console.log('使用ArrayBounds解析边界')
        } else {
          throw new Error('ArrayBounds边界解析失败')
        }
      } else if (bounds.contains && typeof bounds.contains === 'function') {
        // 使用contains方法进行检测
        console.log('使用contains方法进行边界检查')
        return this.allTowers.filter(tower => {
          try {
            const [lng, lat] = tower.position
            const point = { lng, lat }
            return bounds.contains(point)
          } catch (error) {
            return false
          }
        })
      } else {
        // 无法识别的边界格式，使用备用方案
        console.warn('无法识别的边界对象格式，使用备用方案')
        return this.getVisibleTowersByCenter()
      }

      // 验证边界数据的有效性
      if (swLng === undefined || swLat === undefined || neLng === undefined || neLat === undefined ||
          isNaN(swLng) || isNaN(swLat) || isNaN(neLng) || isNaN(neLat)) {
        console.warn('边界数据无效，使用备用方案')
        return this.getVisibleTowersByCenter()
      }

      console.log('解析的边界坐标:', {
        southwest: { lng: swLng, lat: swLat },
        northeast: { lng: neLng, lat: neLat }
      })

      // 添加边界扩展以确保边缘电塔不被遗漏
      const expandRatio = 0.1 // 扩展10%
      const lngRange = neLng - swLng
      const latRange = neLat - swLat
      const expandLng = lngRange * expandRatio
      const expandLat = latRange * expandRatio

      swLng -= expandLng
      swLat -= expandLat
      neLng += expandLng
      neLat += expandLat

      // 筛选视野范围内的电塔
      const visibleTowers = this.allTowers.filter(tower => {
        const [lng, lat] = tower.position
        return lng >= swLng && lng <= neLng && lat >= swLat && lat <= neLat
      })

      return visibleTowers
    } catch (error) {
      console.error('改进边界检测失败:', error)
      return this.getVisibleTowersByCenter()
    }
  }

  /**
   * 备用方案：根据地图中心点和缩放级别获取可见电塔
   * @returns {Array} 视野范围内的电塔数组
   */
  getVisibleTowersByCenter() {
    try {
      const center = this.map.getCenter()
      const zoom = this.map.getZoom()

      if (!center) {
        console.warn('无法获取地图中心点')
        return []
      }

      // 改进的可见范围计算，基于地图缩放级别和屏幕尺寸
      const baseRange = this.calculateViewRange(zoom)

      const centerLng = center.lng || center.getLng()
      const centerLat = center.lat || center.getLat()

      console.log(`备用方案：中心点 [${centerLng}, ${centerLat}], 缩放 ${zoom}, 范围 ${baseRange}`)

      // 使用更精确的距离计算
      const visibleTowers = this.allTowers.filter(tower => {
        const [lng, lat] = tower.position
        const distance = this.calculateDistance(centerLng, centerLat, lng, lat)
        return distance <= baseRange
      })

      console.log(`备用方案找到 ${visibleTowers.length} 个可见电塔`)
      return visibleTowers
    } catch (error) {
      console.error('备用方案获取可见电塔失败:', error)
      return []
    }
  }

  /**
   * 获取距离地图中心最近的N个电塔
   * @param {number} count - 返回的电塔数量
   * @returns {Array} 最近的电塔数组
   */
  getNearestTowers(count = 5) {
    try {
      const center = this.map.getCenter()
      if (!center || !this.allTowers || this.allTowers.length === 0) {
        return []
      }

      const centerLng = center.lng || center.getLng()
      const centerLat = center.lat || center.getLat()

      // 计算所有电塔到中心点的距离
      const towersWithDistance = this.allTowers.map(tower => {
        const [lng, lat] = tower.position
        const distance = this.calculateDistance(centerLng, centerLat, lng, lat)
        return { ...tower, distance }
      })

      // 按距离排序并返回最近的N个
      const nearestTowers = towersWithDistance
        .sort((a, b) => a.distance - b.distance)
        .slice(0, count)
        .map(({ distance, ...tower }) => tower) // 移除distance属性

      console.log(`获取最近的 ${nearestTowers.length} 个电塔`)
      return nearestTowers
    } catch (error) {
      console.error('获取最近电塔失败:', error)
      return []
    }
  }

  /**
   * 根据缩放级别计算可见范围（公里）
   * @param {number} zoom - 缩放级别
   * @returns {number} 可见范围（公里）
   */
  calculateViewRange(zoom) {
    // 基于高德地图的缩放级别计算可见范围
    // 缩放级别越高，可见范围越小
    const baseRange = 200 // 基础范围（公里）
    const zoomFactor = Math.pow(0.5, zoom - 8) // 每增加一级缩放，范围减半
    return Math.max(baseRange * zoomFactor, 5) // 最小范围5公里
  }

  /**
   * 计算两点之间的距离（公里）
   * @param {number} lng1 - 点1经度
   * @param {number} lat1 - 点1纬度
   * @param {number} lng2 - 点2经度
   * @param {number} lat2 - 点2纬度
   * @returns {number} 距离（公里）
   */
  calculateDistance(lng1, lat1, lng2, lat2) {
    const R = 6371 // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  /**
   * 从对象池获取模型
   * @returns {Object|null} 模型对象
   */
  getModelFromPool() {
    if (this.modelPool.length > 0) {
      const model = this.modelPool.pop()
      console.log(`♻️ 从对象池获取模型，剩余: ${this.modelPool.length}/${this.maxPoolSize}`)

      // 验证模型状态
      if (this.validateModel(model)) {
        console.log(`✅ 对象池模型验证通过`)
        return model
      } else {
        console.warn(`⚠️ 对象池模型验证失败，销毁并重试`)
        this.destroyModel(model)
        return this.getModelFromPool() // 递归获取下一个模型
      }
    }
    console.log(`📦 对象池为空，需要创建新模型`)
    return null
  }

  /**
   * 将模型返回到对象池
   * @param {Object} model - 模型对象
   */
  returnModelToPool(model) {
    if (!model) {
      console.warn(`⚠️ 尝试返回空模型到对象池`)
      return
    }

    // 验证模型是否可以回收
    if (!this.validateModel(model)) {
      console.warn(`⚠️ 模型验证失败，直接销毁而不回收`)
      this.destroyModel(model)
      return
    }

    if (this.modelPool.length < this.maxPoolSize) {
      try {
        // 重置模型状态
        this.resetModelState(model)

        // 清理模型的用户数据
        if (model.userData) {
          model.userData = {}
        }

        this.modelPool.push(model)
        console.log(`♻️ 模型已返回对象池，当前池大小: ${this.modelPool.length}/${this.maxPoolSize}`)
      } catch (error) {
        console.error('❌ 返回模型到对象池失败:', error)
        // 如果重置失败，销毁模型而不放入池中
        this.destroyModel(model)
      }
    } else {
      console.log(`📦 对象池已满(${this.maxPoolSize})，销毁模型`)
      this.destroyModel(model)
    }
  }

  /**
   * 验证模型对象的有效性
   * @param {Object} model - 模型对象
   * @returns {boolean} 是否有效
   */
  validateModel(model) {
    try {
      if (!model) {
        console.warn(`🔍 模型验证：模型为空`)
        return false
      }

      // 检查基本属性
      const hasBasicProps = model.hasOwnProperty('position') ||
                           model.hasOwnProperty('setOption') ||
                           model.hasOwnProperty('userData')

      if (!hasBasicProps) {
        console.warn(`🔍 模型验证：缺少基本属性`)
        return false
      }

      console.log(`🔍 模型验证：通过`)
      return true
    } catch (error) {
      console.error(`🔍 模型验证失败:`, error)
      return false
    }
  }

  /**
   * 重置模型状态
   * @param {Object} model - 模型对象
   */
  resetModelState(model) {
    try {
      // 重置位置
      if (model.position) {
        if (typeof model.position.set === 'function') {
          model.position.set(0, 0, 0)
        } else {
          model.position = { x: 0, y: 0, z: 0 }
        }
      }

      // 重置缩放
      if (model.scale) {
        if (typeof model.scale.set === 'function') {
          model.scale.set(1, 1, 1)
        } else {
          model.scale = { x: 1, y: 1, z: 1 }
        }
      }

      // 重置旋转
      if (model.rotation) {
        if (typeof model.rotation.set === 'function') {
          model.rotation.set(0, 0, 0)
        } else {
          model.rotation = { x: 0, y: 0, z: 0 }
        }
      }

      // 重置可见性
      if (model.visible !== undefined) {
        model.visible = true
      }

      // 清理用户数据
      if (model.userData) {
        model.userData = {}
      }

      console.log('♻️ 模型状态已重置')
    } catch (error) {
      console.error('❌ 重置模型状态失败:', error)
      throw error
    }
  }

  /**
   * 重新配置从对象池获取的模型
   * @param {Object} model - 从对象池获取的模型
   * @param {Object} tower - 电塔数据
   */
  async reconfigurePooledModel(model, tower) {
    try {
      console.log(`🔧 重新配置对象池模型: ${tower.id}`)

      // 获取电塔位置和属性
      const lng = tower.longitude || tower.position?.[0] || 0
      const lat = tower.latitude || tower.position?.[1] || 0
      const voltage = tower.info?.voltage || tower.voltage || '500kV'
      const scale = this.getModelScale(voltage)

      // 更新模型位置（对于GLTF模型）
      if (model.setOption && typeof model.setOption === 'function') {
        const modelOptions = {
          position: new this.AMap.LngLat(lng, lat),
          scale: scale,
          height: 0,
          scene: 0,
        }
        model.setOption(modelOptions)
        console.log(`🔧 GLTF模型位置已更新: [${lng}, ${lat}], 缩放: ${scale}`)
      } else if (model.position) {
        // 对于其他类型的模型
        if (typeof model.position.set === 'function') {
          model.position.set(lng, lat, 0)
        } else {
          model.position = { x: lng, y: lat, z: 0 }
        }

        if (model.scale && typeof model.scale.set === 'function') {
          model.scale.set(scale, scale, scale)
        }
        console.log(`🔧 通用模型位置已更新: [${lng}, ${lat}], 缩放: ${scale}`)
      }

      // 更新用户数据
      model.userData = {
        towerId: tower.id,
        towerData: tower,
        isReconfigured: true // 标记为重新配置的模型
      }

      console.log(`✅ 对象池模型重新配置完成: ${tower.id}`)
    } catch (error) {
      console.error(`❌ 重新配置对象池模型失败 ${tower.id}:`, error)
      throw error
    }
  }

  /**
   * 销毁模型对象
   * @param {Object} model - 模型对象
   */
  destroyModel(model) {
    try {
      // 如果模型有dispose方法，调用它
      if (model && typeof model.dispose === 'function') {
        model.dispose()
      }

      // 清理几何体
      if (model.geometry && typeof model.geometry.dispose === 'function') {
        model.geometry.dispose()
      }

      // 清理材质
      if (model.material) {
        if (Array.isArray(model.material)) {
          model.material.forEach(mat => {
            if (mat && typeof mat.dispose === 'function') {
              mat.dispose()
            }
          })
        } else if (typeof model.material.dispose === 'function') {
          model.material.dispose()
        }
      }

      console.log('模型已销毁')
    } catch (error) {
      console.error('销毁模型失败:', error)
    }
  }

  /**
   * 更新电塔模型数据
   * @param {Object} model - 3D模型对象
   * @param {Object} tower - 电塔数据
   */
  updateTowerModelData(model, tower) {
    try {
      console.log(`更新模型数据: ${tower.id}`)

      // 获取电塔位置
      const lng = tower.longitude || tower.position?.[0] || 0
      const lat = tower.latitude || tower.position?.[1] || 0
      const voltage = tower.info?.voltage || tower.voltage || '500kV'

      console.log(`模型位置更新: [${lng}, ${lat}], 电压: ${voltage}`)

      // 设置模型位置 - 使用高德地图的坐标系统
      if (model.setOption && typeof model.setOption === 'function') {
        // GLTF模型使用setOption方法
        const scale = this.getModelScale(voltage)
        model.setOption({
          position: new this.AMap.LngLat(lng, lat),
          scale: scale,
          height: 0,
          scene: 0
        })
        console.log(`✓ GLTF模型位置已更新: [${lng}, ${lat}], 缩放: ${scale}`)
      } else if (model.position) {
        // 其他类型的模型使用position属性
        if (typeof model.position.set === 'function') {
          model.position.set(lng, lat, 0)
        } else {
          model.position = { x: lng, y: lat, z: 0 }
        }

        // 设置模型缩放
        const scale = this.getModelScale(voltage)
        if (model.scale) {
          if (typeof model.scale.set === 'function') {
            model.scale.set(scale, scale, scale)
          } else {
            model.scale = { x: scale, y: scale, z: scale }
          }
        }
        console.log(`✓ 通用模型位置已更新: [${lng}, ${lat}], 缩放: ${scale}`)
      }

      // 存储电塔数据到模型
      if (!model.userData) {
        model.userData = {}
      }
      model.userData.towerId = tower.id
      model.userData.towerData = tower
      model.userData.lastUpdate = Date.now()

      console.log(`✓ 模型数据更新完成: ${tower.id}`)
    } catch (error) {
      console.error(`更新电塔模型数据失败 ${tower.id}:`, error)
    }
  }

  /**
   * 根据电压等级获取模型缩放比例
   * @param {string} voltage - 电压等级
   * @returns {number} 缩放比例
   */
  getModelScale(voltage) {
    const baseScale = 80
    const zoomLevel = this.map ? this.map.getZoom() : 12

    // 根据电压等级调整基础缩放 - 支持多种格式
    let voltageScale = 1
    const normalizedVoltage = voltage?.toUpperCase() || ''

    if (normalizedVoltage.includes('500')) {
      voltageScale = 1.5
    } else if (normalizedVoltage.includes('220')) {
      voltageScale = 1.2
    } else if (normalizedVoltage.includes('110')) {
      voltageScale = 1.0
    } else if (normalizedVoltage.includes('35')) {
      voltageScale = 0.8
    } else {
      voltageScale = 1.0
    }

    // 根据缩放等级调整
    const zoomScale = Math.pow(0.9, zoomLevel - 12)

    return baseScale * voltageScale * zoomScale
  }

  /**
   * 创建杆塔3D模型实例
   * @param {Object} tower - 电塔数据
   * @returns {Promise} 模型创建Promise
   */
  async create3DTowerModel(tower) {
    try {
      console.log('开始创建3D电塔模型:', tower.id)
      console.log('模型路径:', this.modelPath)

      // 检查是否支持GLTF模型加载
      if (!this.AMap.GltfLoader) {
        console.warn('AMap.GltfLoader不可用，请确保已加载GltfLoader插件')
        return null
      }

      // 创建GLTF加载器
      const gltfLoader = new this.AMap.GltfLoader()

      // 加载GLTF模型 - 使用Promise包装，参考老版本的实现
      const gltfModel = await new Promise((resolve, reject) => {
        gltfLoader.load(this.modelPath, (gltf) => {
          resolve(gltf)
        }, (error) => {
          reject(error)
        })
      })

      console.log(`✓ GLTF模型加载成功: ${tower.id}`)

      // 设置模型位置和属性 - 参考老版本的数据结构
      const lng = tower.longitude || tower.position?.[0] || 0
      const lat = tower.latitude || tower.position?.[1] || 0
      const voltage = tower.info?.voltage || tower.voltage || '500kV'
      const scale = this.getModelScale(voltage)

      console.log(`模型参数 - 位置: [${lng}, ${lat}], 电压: ${voltage}, 缩放: ${scale}`)

      const modelOptions = {
        position: new this.AMap.LngLat(lng, lat),
        scale: scale,
        height: 0, // 离地高度
        scene: 0,
      }

      // 应用模型配置
      gltfModel.setOption(modelOptions)

      // 设置模型旋转（参考老项目的设置）
      gltfModel.rotateX(90)
      gltfModel.rotateZ(10)

      // 存储电塔数据到模型
      gltfModel.userData = {
        towerId: tower.id,
        towerData: tower
      }

      console.log(`✓ 3D电塔模型创建成功: ${tower.id}`)
      return gltfModel

    } catch (error) {
      console.error(`创建杆塔 ${tower.id} 的3D模型失败:`, error)
      return null
    }
  }

  /**
   * 创建备用模型（当GLTF加载失败时使用）
   * @param {Object} tower - 电塔数据
   * @returns {Promise} 备用模型创建Promise
   */
  async createFallbackModel(tower) {
    return new Promise((resolve, reject) => {
      try {
        console.log('创建备用几何体模型:', tower.id)

        // 检查是否支持Object3D
        if (!this.AMap.Object3D || !this.AMap.Object3D.Mesh) {
          console.warn('Object3D不可用，无法创建备用模型')
          resolve(null)
          return
        }

        // 创建3D模型对象
        const model = new this.AMap.Object3D.Mesh()

        // 设置模型位置
        const lng = tower.longitude || tower.position?.[0] || 0
        const lat = tower.latitude || tower.position?.[1] || 0
        if (model.position) {
          model.position.set(lng, lat, 0)
        }

        // 设置模型缩放
        const scale = this.getModelScale(tower.voltage)
        if (model.scale) {
          model.scale.set(scale, scale, scale)
        }

        // 存储电塔数据到模型
        model.userData = {
          towerId: tower.id,
          towerData: tower,
          isFallback: true // 标记为备用模型
        }

        // 创建几何体和材质
        if (this.AMap.Object3D.BoxGeometry && this.AMap.Object3D.MeshBasicMaterial) {
          const geometry = new this.AMap.Object3D.BoxGeometry(2, 2, tower.height || 45)
          const material = new this.AMap.Object3D.MeshBasicMaterial({
            color: this.getTowerColor(tower.voltage)
          })
          const mesh = new this.AMap.Object3D.Mesh(geometry, material)
          model.add(mesh)
          console.log(`备用3D电塔模型创建成功: ${tower.id}`)
        }

        resolve(model)

      } catch (error) {
        console.error(`创建备用模型失败 ${tower.id}:`, error)
        reject(error)
      }
    })
  }

  /**
   * 根据电压等级获取电塔颜色
   * @param {string} voltage - 电压等级
   * @returns {number} 颜色值
   */
  getTowerColor(voltage) {
    const colorMap = {
      '500KV': 0x1890ff, // 蓝色 - 统一颜色
      '220KV': 0x1890ff, // 蓝色 - 统一颜色
      '110KV': 0x1890ff, // 蓝色 - 统一颜色
      '35KV': 0x1890ff,  // 蓝色 - 统一颜色
      '10KV': 0x1890ff   // 蓝色 - 统一颜色
    }
    return colorMap[voltage] || 0x1890ff
  }

  /**
   * 获取加载状态摘要
   * @returns {Object} 状态摘要
   */
  getLoadSummary() {
    return {
      isLoading: this.isLoading,
      visibleModelCount: this.visibleModels.size,
      totalTowers: this.allTowers.length,
      shouldLoad: this.shouldLoad3DModels(),
      currentZoom: this.map.getZoom(),
      minZoom: this.minZoom,
      viewMode: this.getViewMode(),
      loadError: this.loadError,
      has3DLayer: !!this.object3DLayer,
      hasSuccessfullyLoadedModels: this.hasSuccessfullyLoadedModels
    }
  }

  /**
   * 调试方法：打印当前状态
   */
  debugStatus() {
    const summary = this.getLoadSummary()
    console.log('=== 3D模型管理器状态 ===')
    console.log('是否正在加载:', summary.isLoading)
    console.log('可见模型数量:', summary.visibleModelCount)
    console.log('总电塔数量:', summary.totalTowers)
    console.log('是否应该加载3D模型:', summary.shouldLoad)
    console.log('当前缩放级别:', summary.currentZoom)
    console.log('最小缩放级别:', summary.minZoom)
    console.log('视图模式:', summary.viewMode)
    console.log('是否有3D图层:', summary.has3DLayer)
    console.log('是否成功加载过模型:', summary.hasSuccessfullyLoadedModels)
    console.log('加载错误:', summary.loadError)
    console.log('对象池大小:', this.modelPool.length)
    console.log('最大模型数量:', this.maxModels)

    // 详细的模型信息
    if (this.visibleModels.size > 0) {
      console.log('可见模型详情:')
      let index = 0
      this.visibleModels.forEach((modelData, modelId) => {
        if (index < 5) { // 只显示前5个
          console.log(`  ${index + 1}. ${modelId}:`, {
            towerId: modelData.tower?.id,
            towerName: modelData.tower?.name,
            position: modelData.tower?.position,
            hasModel: !!modelData.model
          })
        }
        index++
      })
      if (this.visibleModels.size > 5) {
        console.log(`  ... 还有 ${this.visibleModels.size - 5} 个模型`)
      }
    }

    console.log('========================')
    return summary
  }

  /**
   * 获取详细的性能统计信息
   */
  getPerformanceStats() {
    const stats = {
      timestamp: new Date().toISOString(),
      visibleModelCount: this.visibleModels.size,
      poolSize: this.modelPool.length,
      totalTowers: this.allTowers.length,
      isLoading: this.isLoading,
      hasSuccessfullyLoadedModels: this.hasSuccessfullyLoadedModels,
      currentZoom: this.map?.getZoom() || 0,
      viewMode: this.getViewMode(),
      shouldLoad: this.shouldLoad3DModels(),
      memoryUsage: {
        visibleModels: this.visibleModels.size,
        pooledModels: this.modelPool.length,
        totalManaged: this.visibleModels.size + this.modelPool.length
      }
    }

    return stats
  }

  /**
   * 启动性能监控
   * @param {number} interval - 监控间隔（毫秒）
   */
  startPerformanceMonitoring(interval = 5000) {
    if (this.performanceMonitorTimer) {
      clearInterval(this.performanceMonitorTimer)
    }

    console.log(`启动3D模型性能监控，间隔: ${interval}ms`)

    this.performanceMonitorTimer = setInterval(() => {
      const stats = this.getPerformanceStats()
      console.log('📊 3D模型性能统计:', stats)

      // 检查异常情况
      if (stats.visibleModelCount === 0 && stats.shouldLoad && stats.totalTowers > 0) {
        console.warn('⚠️ 检测到异常：应该显示模型但没有可见模型')
        this.debugStatus()
      }

      if (stats.visibleModelCount > this.maxModels * 1.2) {
        console.warn('⚠️ 检测到异常：可见模型数量超过限制')
      }
    }, interval)
  }

  /**
   * 停止性能监控
   */
  stopPerformanceMonitoring() {
    if (this.performanceMonitorTimer) {
      clearInterval(this.performanceMonitorTimer)
      this.performanceMonitorTimer = null
      console.log('停止3D模型性能监控')
    }
  }

  /**
   * 处理缩放变化
   */
  handleZoomChange() {
    const currentZoom = this.map.getZoom()
    const previousZoom = this.lastZoom || 0
    this.lastZoom = currentZoom

    console.log(`🔍 3D模型管理器：缩放变化 ${previousZoom} -> ${currentZoom}`)

    // 清除之前的缩放处理定时器
    if (this.zoomChangeTimer) {
      clearTimeout(this.zoomChangeTimer)
    }

    // 使用防抖处理缩放事件，避免频繁触发
    this.zoomChangeTimer = setTimeout(() => {
      // 检查是否正在加载中
      if (this.isLoading) {
        console.log('🔍 缩放处理：正在加载中，跳过此次缩放处理')
        return
      }

      const shouldLoad = this.shouldLoad3DModels()
      console.log(`🔍 缩放处理：当前缩放=${currentZoom}, 应该加载3D模型=${shouldLoad}`)

      if (shouldLoad) {
        // 缩放级别足够，检查是否需要更新模型
        const mapCenter = this.map.getCenter()
        const currentCenter = `${mapCenter.lng},${mapCenter.lat}`

        // 如果位置没有变化且已有模型显示，跳过重复加载
        if (this.lastCenter === currentCenter && this.visibleModels.size > 0) {
          console.log('🔍 缩放处理：位置未变化且已有模型显示，跳过重复加载')
          return
        }

        this.lastCenter = currentCenter
        console.log('🔍 缩放级别足够，开始管理3D模型显示')
        this.manage3DModels()
      } else if (currentZoom < this.minZoom) {
        // 缩放级别不够，隐藏所有模型
        console.log('🔍 缩放级别不足，隐藏3D模型')
        this.hide3DModels()
        this.lastCenter = null // 重置位置记录
      }
    }, 200) // 200ms防抖延迟
  }

  /**
   * 处理地图移动
   */
  handleMapMove() {
    // 延迟执行以避免频繁调用
    if (this.handleMapMove.timeout) {
      clearTimeout(this.handleMapMove.timeout)
    }

    // 增加防抖延迟，减少频繁更新
    const debounceDelay = this.debounceDelay * 1.5 // 增加50%的延迟

    this.handleMapMove.timeout = setTimeout(() => {
      // 检查是否正在加载中，避免重复触发
      if (this.isLoading) {
        console.log('地图移动：正在加载中，跳过此次更新')
        return
      }

      console.log('=== 地图移动事件触发 ===')
      console.log('当前地图中心:', this.map.getCenter())
      console.log('当前缩放级别:', this.map.getZoom())
      console.log('当前可见模型数量:', this.visibleModels.size)
      console.log('总电塔数量:', this.allTowers.length)

      // 调试：输出当前状态
      const shouldLoad = this.shouldLoad3DModels()
      console.log('是否应该加载3D模型:', shouldLoad)
      console.log('- 视图模式:', this.getViewMode())
      console.log('- 当前缩放:', this.map.getZoom())
      console.log('- 最小缩放:', this.minZoom)
      console.log('- 3D图层存在:', !!this.object3DLayer)

      // 智能更新模型显示
      if (shouldLoad) {
        console.log('开始智能更新3D模型...')
        // 使用Promise处理，避免阻塞
        this.smartUpdateModels().catch(error => {
          console.error('智能更新3D模型失败:', error)
        })
      } else {
        console.log('不满足3D模型显示条件，隐藏所有模型')
        this.hide3DModels()
      }
      console.log('=== 地图移动处理完成 ===')
    }, debounceDelay)
  }

  /**
   * 智能更新3D模型显示
   * 只更新视野变化的部分，避免全部重新加载
   */
  async smartUpdateModels() {
    if (this.isLoading || this.isUpdatingModels) {
      console.log('🔄 正在加载或更新中，跳过智能更新')
      return
    }

    try {
      this.isUpdatingModels = true
      console.log('=== 🔄 开始智能更新3D模型 ===')
      console.log('当前已显示模型数量:', this.visibleModels.size)

      // 检查视野是否发生了显著变化
      const currentBounds = this.map.getBounds()
      const currentCenter = this.map.getCenter()
      const currentZoom = this.map.getZoom()

      // 安全地获取边界和中心点坐标
      let boundsKey, centerKey
      try {
        // 尝试不同的方式获取边界坐标
        if (currentBounds && currentBounds.southwest && currentBounds.northeast) {
          // 标准格式
          boundsKey = `${currentBounds.southwest.lng},${currentBounds.southwest.lat},${currentBounds.northeast.lng},${currentBounds.northeast.lat}`
        } else if (currentBounds && currentBounds.getSouthWest && currentBounds.getNorthEast) {
          // 方法格式
          const sw = currentBounds.getSouthWest()
          const ne = currentBounds.getNorthEast()
          boundsKey = `${sw.lng || sw.getLng()},${sw.lat || sw.getLat()},${ne.lng || ne.getLng()},${ne.lat || ne.getLat()}`
        } else {
          // 备用方案：使用中心点和缩放级别
          boundsKey = `center_${currentCenter.lng || currentCenter.getLng()}_${currentCenter.lat || currentCenter.getLat()}_zoom_${currentZoom}`
        }

        // 获取中心点坐标
        centerKey = `${currentCenter.lng || currentCenter.getLng()},${currentCenter.lat || currentCenter.getLat()}`
      } catch (error) {
        console.error('获取地图边界信息失败:', error)
        // 使用缩放级别作为备用标识
        boundsKey = `zoom_${currentZoom}_${Date.now()}`
        centerKey = `center_${Date.now()}`
      }

      // 如果视野没有显著变化且已有模型，跳过更新
      if (this.lastBoundsKey === boundsKey && this.visibleModels.size > 0) {
        console.log('🔄 视野未发生显著变化，跳过智能更新')
        return
      }

      this.lastBoundsKey = boundsKey
      console.log(`🔄 视野变化检测: 中心=${centerKey}, 缩放=${currentZoom}`)

      // 获取当前视野内的电塔
      const currentVisibleTowers = this.getVisibleTowersInView()
      console.log(`当前视野内发现 ${currentVisibleTowers.length} 个电塔`)

      // 如果没有找到任何电塔，清除所有模型
      if (currentVisibleTowers.length === 0) {
        console.warn('⚠️ 视野内没有发现任何电塔，清除所有模型')
        this.clearVisibleModels()
        this.hasSuccessfullyLoadedModels = false
        this.notifyMassMarkerManager()
        return
      }

      // 创建当前视野内电塔ID的集合
      const currentVisibleIds = new Set(currentVisibleTowers.map(tower => `tower-model-${tower.id}`))

      // 调试：输出当前视野内的电塔信息
      if (currentVisibleTowers.length > 0) {
        console.log('视野内电塔详情:', currentVisibleTowers.slice(0, 5).map(t => ({
          id: t.id,
          name: t.name,
          position: t.position,
          voltage: t.voltage
        })))
      }

      // 找出需要移除的模型（不在当前视野内的）
      const modelsToRemove = []
      this.visibleModels.forEach((modelData, modelId) => {
        if (!currentVisibleIds.has(modelId)) {
          modelsToRemove.push({ modelId, modelData })
        }
      })

      // 找出需要添加的电塔（当前视野内但还没有模型的）
      const towersToAdd = currentVisibleTowers.filter(tower => {
        const modelId = `tower-model-${tower.id}`
        return !this.visibleModels.has(modelId)
      })

      console.log(`📊 更新统计: 需要移除 ${modelsToRemove.length} 个模型，需要添加 ${towersToAdd.length} 个模型`)

      // 调试：输出需要添加和移除的详情
      if (modelsToRemove.length > 0) {
        console.log('需要移除的模型:', modelsToRemove.slice(0, 3).map(item => item.modelId))
      }
      if (towersToAdd.length > 0) {
        console.log('需要添加的电塔:', towersToAdd.slice(0, 3).map(t => ({
          id: t.id,
          name: t.name,
          position: t.position
        })))
      }

      // 移除不在视野内的模型
      let removedCount = 0
      modelsToRemove.forEach(({ modelId, modelData }) => {
        if (modelData && modelData.model && this.object3DLayer) {
          try {
            this.object3DLayer.remove(modelData.model)
            this.returnModelToPool(modelData.model)
            this.visibleModels.delete(modelId)
            removedCount++
            console.log(`✓ 移除模型: ${modelId}`)
          } catch (error) {
            console.error(`移除模型失败 ${modelId}:`, error)
          }
        }
      })

      // 限制新增模型数量以保持性能
      let towersToAddLimited = towersToAdd
      const currentModelCount = this.visibleModels.size
      const availableSlots = this.maxModels - currentModelCount

      if (towersToAdd.length > availableSlots && availableSlots > 0) {
        // 按电压等级和距离排序，优先显示重要且近的电塔
        const center = this.map.getCenter()
        const centerLng = center.lng || center.getLng()
        const centerLat = center.lat || center.getLat()

        towersToAddLimited = towersToAdd
          .map(tower => {
            const [lng, lat] = tower.position
            const distance = this.calculateDistance(centerLng, centerLat, lng, lat)
            return { ...tower, distance }
          })
          .sort((a, b) => {
            // 先按电压等级排序，再按距离排序
            const voltageOrder = { '500KV': 4, '220KV': 3, '110KV': 2, '35KV': 1, '10KV': 0 }
            const aVoltage = voltageOrder[a.voltage] || 0
            const bVoltage = voltageOrder[b.voltage] || 0

            if (aVoltage !== bVoltage) {
              return bVoltage - aVoltage // 电压等级高的优先
            }
            return a.distance - b.distance // 距离近的优先
          })
          .slice(0, availableSlots)
          .map(({ distance, ...tower }) => tower) // 移除distance属性

        console.log(`限制新增模型数量为 ${availableSlots} 个（总限制: ${this.maxModels}）`)
      }

      // 在添加新模型前，清理可能的重复模型
      this.cleanupDuplicateModels()

      // 批量添加新模型
      let addedCount = 0
      if (towersToAddLimited.length > 0) {
        addedCount = await this.addModelsForTowers(towersToAddLimited)
      }

      // 更新成功加载状态
      this.hasSuccessfullyLoadedModels = this.visibleModels.size > 0

      console.log(`智能更新完成: 移除 ${removedCount} 个，添加 ${addedCount} 个，当前显示 ${this.visibleModels.size} 个3D模型`)

      // 通知海量点管理器更新2D图标显示状态
      this.notifyMassMarkerManager()

    } catch (error) {
      console.error('智能更新3D模型失败:', error)
      this.hasSuccessfullyLoadedModels = false
    } finally {
      this.isUpdatingModels = false
    }
  }

  /**
   * 清理重复的3D模型
   * 检查并移除位置过于接近的重复模型
   */
  cleanupDuplicateModels() {
    console.log('🧹 开始清理重复的3D模型...')

    const modelsToRemove = []
    const processedPositions = new Map()

    // 遍历所有可见模型，检查重复位置
    for (const [modelId, modelData] of this.visibleModels) {
      const tower = modelData.tower
      if (!tower || !tower.position) continue

      const positionKey = `${tower.position[0]},${tower.position[1]}`

      if (processedPositions.has(positionKey)) {
        // 发现重复位置，标记为待移除
        console.log(`⚠️ 发现重复位置模型，标记移除: ${tower.id} (位置: ${positionKey})`)
        modelsToRemove.push(modelId)
      } else {
        processedPositions.set(positionKey, modelId)
      }
    }

    // 移除重复模型
    let removedCount = 0
    modelsToRemove.forEach(modelId => {
      const modelData = this.visibleModels.get(modelId)
      if (modelData && modelData.model && this.object3DLayer) {
        this.object3DLayer.remove(modelData.model)
        this.returnModelToPool(modelData.model)
        this.visibleModels.delete(modelId)
        removedCount++
      }
    })

    if (removedCount > 0) {
      console.log(`🧹 清理完成，移除了 ${removedCount} 个重复模型`)
    } else {
      console.log('✅ 没有发现重复模型')
    }
  }

  /**
   * 为指定的电塔添加3D模型
   * @param {Array} towers - 电塔数组
   * @returns {number} 成功添加的模型数量
   */
  async addModelsForTowers(towers) {
    // 防止并发创建模型
    if (this.isCreatingModels) {
      console.log('⚠️ 模型创建正在进行中，跳过本次创建')
      return 0
    }

    this.isCreatingModels = true
    let successCount = 0

    try {
      console.log(`🏗️ 开始批量添加 ${towers.length} 个3D模型`)

    // 预先过滤掉已存在的模型，增强去重检查
    const filteredTowers = []
    const positionMap = new Map() // 用于检查位置重复
    const processedPositions = new Set() // 用于检查本批次内的位置重复

    for (const tower of towers) {
      const modelId = `tower-model-${tower.id}`
      const positionKey = `${tower.position[0]},${tower.position[1]}`

      // 检查模型ID是否已存在
      if (this.visibleModels.has(modelId)) {
        console.log(`⚠️ 模型ID已存在，跳过: ${tower.id}`)
        continue
      }

      // 检查本批次内位置是否重复
      if (processedPositions.has(positionKey)) {
        console.log(`⚠️ 本批次内位置重复，跳过: ${tower.id} (位置: ${positionKey})`)
        continue
      }

      // 检查位置是否重复（防止同一位置多个模型）
      if (positionMap.has(positionKey)) {
        console.log(`⚠️ 位置重复，跳过: ${tower.id} (位置: ${positionKey})`)
        continue
      }

      // 检查是否与现有模型位置过于接近
      let tooClose = false
      for (const [, existingModelData] of this.visibleModels) {
        const existingTower = existingModelData.tower
        if (existingTower && existingTower.position) {
          const distance = this.calculateDistance(
            tower.position[0], tower.position[1],
            existingTower.position[0], existingTower.position[1]
          )
          // 如果距离小于50米，认为是重复位置
          if (distance < 0.05) { // 约50米
            console.log(`⚠️ 位置过于接近现有模型，跳过: ${tower.id} (距离: ${(distance * 1000).toFixed(1)}m)`)
            tooClose = true
            break
          }
        }
      }

      if (!tooClose) {
        filteredTowers.push(tower)
        positionMap.set(positionKey, tower.id)
        processedPositions.add(positionKey) // 记录本批次已处理的位置
      }
    }

    console.log(`🏗️ 过滤后需要添加 ${filteredTowers.length} 个模型 (原始: ${towers.length})`)

    if (filteredTowers.length === 0) {
      console.log('🏗️ 没有需要添加的新模型')
      return 0
    }

    // 批量处理模型加载
    const batches = []
    for (let i = 0; i < filteredTowers.length; i += this.loadBatchSize) {
      batches.push(filteredTowers.slice(i, i + this.loadBatchSize))
    }

    // 逐批加载模型
    for (const batch of batches) {
      const results = await Promise.allSettled(batch.map(async (tower) => {
        const modelId = `tower-model-${tower.id}`

        try {
          console.log(`🏗️ 添加3D模型: ${tower.id}`)

          // 最后一次检查（防止并发问题）
          if (this.visibleModels.has(modelId)) {
            console.log(`⚠️ 模型已存在（并发检查），跳过: ${tower.id}`)
            return false
          }

          // 最后一次位置检查（防止并发问题导致的位置重复）
          for (const [, existingModelData] of this.visibleModels) {
            const existingTower = existingModelData.tower
            if (existingTower && existingTower.position) {
              const distance = this.calculateDistance(
                tower.position[0], tower.position[1],
                existingTower.position[0], existingTower.position[1]
              )
              // 如果距离小于10米，认为是重复位置（更严格的检查）
              if (distance < 0.01) { // 约10米
                console.log(`⚠️ 最终位置检查：位置过于接近，跳过: ${tower.id} (距离: ${(distance * 1000).toFixed(1)}m)`)
                return false
              }
            }
          }

          // 从对象池获取或创建新模型
          let model = this.getModelFromPool()
          let isFromPool = !!model

          if (!model) {
            console.log(`🆕 创建新模型: ${tower.id}`)
            model = await this.create3DTowerModel(tower)
          } else {
            console.log(`♻️ 使用对象池中的模型: ${tower.id}`)
            // 确保从对象池获取的模型被正确重置
            this.resetModelState(model)
            // 重新配置模型为当前电塔
            await this.reconfigurePooledModel(model, tower)
          }

          if (model) {
            // 更新模型位置和数据（对新模型和池中模型都需要）
            this.updateTowerModelData(model, tower)

            // 添加到3D图层
            if (this.object3DLayer) {
              this.object3DLayer.add(model)
              console.log(`✅ 模型已添加到3D图层: ${tower.id} (${isFromPool ? '来自对象池' : '新创建'})`)
            }

            // 记录可见模型
            this.visibleModels.set(modelId, {
              model: model,
              tower: tower,
              isFromPool: isFromPool // 记录模型来源，便于调试
            })

            console.log(`✅ 成功添加3D模型: ${tower.id}`)
            return true
          } else {
            console.warn(`❌ 模型创建失败: ${tower.id}`)
            return false
          }
        } catch (error) {
          console.error(`❌ 添加3D模型失败 ${tower.id}:`, error)
          return false
        }
      }))

      // 统计成功的数量
      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value === true) {
          successCount++
        }
      })
    }

      console.log(`批量添加完成: 成功 ${successCount}/${towers.length} 个模型`)
      return successCount
    } catch (error) {
      console.error('批量添加模型失败:', error)
      return 0
    } finally {
      this.isCreatingModels = false
    }
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器执行错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 清除可见的3D模型（不清理对象池）
   */
  clearVisibleModels() {
    try {
      console.log(`🧹 开始清除可见的3D模型，当前数量: ${this.visibleModels.size}`)

      let removedCount = 0
      let pooledCount = 0
      let errorCount = 0

      // 清除可见模型
      this.visibleModels.forEach((modelData, modelId) => {
        try {
          if (modelData.model && this.object3DLayer) {
            // 从3D图层中移除
            this.object3DLayer.remove(modelData.model)
            removedCount++

            // 将模型返回对象池
            this.returnModelToPool(modelData.model)
            pooledCount++

            console.log(`✅ 清除可见模型: ${modelId}`)
          } else {
            console.warn(`⚠️ 模型数据不完整: ${modelId}`)
          }
        } catch (error) {
          console.error(`❌ 清除模型失败 ${modelId}:`, error)
          errorCount++
        }
      })

      // 清空可见模型记录
      this.visibleModels.clear()

      console.log(`✅ 可见模型清除完成: 移除${removedCount}个，回收${pooledCount}个，错误${errorCount}个`)
    } catch (error) {
      console.error('❌ 清除可见3D模型失败:', error)
    }
  }

  /**
   * 清除所有3D模型
   */
  clear3DModels() {
    try {
      console.log('🧹 开始清除所有3D模型...')

      // 清除可见模型
      this.clearVisibleModels()

      // 清理对象池中的所有模型
      this.clearModelPool()

      // 重置状态
      this.hasSuccessfullyLoadedModels = false

      // 清除定时器
      if (this.modelManageTimer) {
        clearTimeout(this.modelManageTimer)
        this.modelManageTimer = null
      }

      console.log('✅ 所有3D模型已清除')
    } catch (error) {
      console.error('❌ 清除3D模型失败:', error)
    }
  }

  /**
   * 清理对象池
   */
  clearModelPool() {
    try {
      console.log(`清理对象池，当前大小: ${this.modelPool.length}`)

      // 销毁对象池中的所有模型
      this.modelPool.forEach(model => {
        this.destroyModel(model)
      })

      // 清空对象池
      this.modelPool = []

      console.log('对象池已清理')
    } catch (error) {
      console.error('清理对象池失败:', error)
    }
  }

  /**
   * 完全销毁3D系统
   */
  destroy3DSystem() {
    try {
      console.log('开始销毁3D系统...')

      // 清除所有模型
      this.clear3DModels()

      // 清除3D对象图层
      if (this.object3DLayer && this.map) {
        this.map.remove(this.object3DLayer)
        this.object3DLayer = null
      }

      console.log('3D系统已完全销毁')
    } catch (error) {
      console.error('销毁3D系统失败:', error)
    }
  }

  /**
   * 调试方法：输出当前模型状态
   */
  debugModelStatus() {
    console.log('=== 3D模型状态调试 ===')
    console.log('总电塔数量:', this.allTowers.length)
    console.log('可见模型数量:', this.visibleModels.size)
    console.log('对象池大小:', this.modelPool.length)

    if (this.visibleModels.size > 0) {
      console.log('可见模型详情:')
      const modelDetails = []
      for (const [modelId, modelData] of this.visibleModels) {
        const tower = modelData.tower
        modelDetails.push({
          modelId,
          towerId: tower?.id,
          position: tower?.position,
          voltage: tower?.voltage
        })
      }
      console.table(modelDetails)

      // 检查位置重复
      const positionMap = new Map()
      const duplicates = []

      for (const [modelId, modelData] of this.visibleModels) {
        const tower = modelData.tower
        if (tower && tower.position) {
          const positionKey = `${tower.position[0]},${tower.position[1]}`
          if (positionMap.has(positionKey)) {
            duplicates.push({
              position: positionKey,
              models: [positionMap.get(positionKey), modelId]
            })
          } else {
            positionMap.set(positionKey, modelId)
          }
        }
      }

      if (duplicates.length > 0) {
        console.warn('⚠️ 发现重复位置的模型:', duplicates)
      } else {
        console.log('✅ 没有重复位置的模型')
      }
    }

    console.log('=== 调试结束 ===')
  }

  /**
   * 强制重新加载所有3D模型
   * 清除所有现有模型并重新创建
   */
  forceReload3DModels() {
    console.log('🔄 强制重新加载所有3D模型...')

    // 重置所有状态
    this.isLoading = false
    this.isUpdatingModels = false
    this.isCreatingModels = false

    // 清除所有现有模型
    this.clearVisibleModels()

    // 清空对象池
    this.modelPool.forEach(model => {
      try {
        this.destroyModel(model)
      } catch (error) {
        console.warn('销毁对象池模型失败:', error)
      }
    })
    this.modelPool = []

    // 重新加载电塔数据
    this.loadTowerPositions()

    // 如果满足显示条件，重新加载模型
    if (this.shouldLoad3DModels()) {
      console.log('开始重新加载3D模型...')
      setTimeout(() => {
        this.manage3DModels()
      }, 500) // 延迟一点执行，确保清理完成
    } else {
      console.log('不满足3D模型显示条件，跳过重新加载')
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    console.log('开始销毁Model3DManager...')

    // 完全销毁3D系统
    this.destroy3DSystem()

    // 清除定时器
    if (this.modelManageTimer) {
      clearTimeout(this.modelManageTimer)
      this.modelManageTimer = null
    }

    if (this.zoomChangeTimer) {
      clearTimeout(this.zoomChangeTimer)
      this.zoomChangeTimer = null
    }

    if (this.handleMapMove.timeout) {
      clearTimeout(this.handleMapMove.timeout)
      this.handleMapMove.timeout = null
    }

    // 停止性能监控
    this.stopPerformanceMonitoring()

    // 移除地图事件监听
    if (this.map) {
      this.map.off('zoomchange')
      this.map.off('mapmove')
      this.map.off('dragend')
      this.map.off('moveend')
      this.map.off('complete')
    }

    // 清理数据
    this.allTowers = []
    this.eventListeners.clear()
    this.visibleBounds = null
    this.loadError = null
    this.massMarkerManager = null

    // 重置状态
    this.isLoading = false
    this.hasSuccessfullyLoadedModels = false

    // 清理引用
    this.map = null
    this.AMap = null

    console.log('Model3DManager 已完全销毁')
  }
}

export default Model3DManager
