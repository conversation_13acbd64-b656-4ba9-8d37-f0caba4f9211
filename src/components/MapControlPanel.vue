<template>
  <div class="control-panel" :class="{ 'collapsed': isCollapsed }">
    <!-- 控制台标题栏 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon class="title-icon"><Setting /></el-icon>
        地图控制台
      </h3>
      <div class="header-actions">
        <el-button
          class="mode-switch-btn"
          :icon="panelMode === 'detailed' ? Grid : Document"
          size="small"
          text
          @click="togglePanelMode"
          :title="panelMode === 'detailed' ? '切换到简洁模式' : '切换到详细模式'"
        />
        <el-button
          class="collapse-btn"
          :icon="isCollapsed ? Expand : Fold"
          size="small"
          text
          @click="toggleCollapse"
        />
      </div>
    </div>

    <!-- 控制台内容 -->
    <div class="panel-content" v-show="!isCollapsed">
      
      <!-- 地图模式切换区域 -->
      <div class="control-section map-mode-section">
        <div class="section-header">
          <el-icon class="section-icon"><View /></el-icon>
          <span class="section-title">地图模式</span>
        </div>
        <div class="section-content">
          <el-radio-group
            v-model="mapMode"
            class="mode-radio-group"
            @change="handleMapModeChange"
            :disabled="switchingMode"
          >
            <el-radio-button label="2D" value="2D" class="mode-button">
              <el-icon class="mode-icon"><Grid /></el-icon>
              <span class="mode-text">2D地图</span>
              <div v-if="mapMode === '2D'" class="mode-indicator"></div>
            </el-radio-button>
            <el-radio-button label="3D" value="3D" class="mode-button">
              <el-icon class="mode-icon"><Box /></el-icon>
              <span class="mode-text">3D地图</span>
              <div v-if="mapMode === '3D'" class="mode-indicator"></div>
            </el-radio-button>
          </el-radio-group>

          <!-- 切换状态提示 -->
          <div v-if="switchingMode" class="switching-indicator">
            <el-icon class="switching-icon"><Loading /></el-icon>
            <span class="switching-text">正在切换到{{ mapMode }}模式...</span>
          </div>
        </div>
      </div>

      <!-- 地图图层切换区域 -->
      <div class="control-section map-layer-section">
        <div class="section-header">
          <el-icon class="section-icon"><Position /></el-icon>
          <span class="section-title">地图图层</span>
        </div>
        <div class="section-content">
          <el-radio-group
            v-model="mapLayer"
            class="layer-radio-group"
            @change="handleMapLayerChange"
            :disabled="switchingLayer"
          >
            <el-radio-button label="normal" value="normal" class="layer-button">
              <el-icon class="layer-icon"><Document /></el-icon>
              <span class="layer-text">普通地图</span>
              <div v-if="mapLayer === 'normal'" class="layer-indicator"></div>
            </el-radio-button>
            <el-radio-button label="satellite" value="satellite" class="layer-button">
              <el-icon class="layer-icon"><Monitor /></el-icon>
              <span class="layer-text">卫星地图</span>
              <div v-if="mapLayer === 'satellite'" class="layer-indicator"></div>
            </el-radio-button>
          </el-radio-group>

          <!-- 图层切换状态提示 -->
          <div v-if="switchingLayer" class="switching-indicator">
            <el-icon class="switching-icon"><Loading /></el-icon>
            <span class="switching-text">正在切换到{{ mapLayer === 'satellite' ? '卫星' : '普通' }}地图...</span>
          </div>
        </div>
      </div>

      <!-- 地名和边界控制区域 -->
      <div class="control-section map-display-section">
        <div class="section-header" @click="toggleMapDisplay">
          <el-icon class="section-icon"><Position /></el-icon>
          <span class="section-title">地图显示</span>
          <el-icon class="expand-icon" :class="{ 'expanded': mapDisplayVisible }">
            <ArrowDown />
          </el-icon>
        </div>
        <div class="section-content" v-show="mapDisplayVisible">
          <div class="display-controls">
            <div class="control-item">
              <el-switch
                v-model="showPlaceNames"
                @change="handlePlaceNamesChange"
                active-text="地名显示"
                inactive-text="地名隐藏"
                size="small"
              />
            </div>
            <div class="control-item">
              <el-switch
                v-model="showBoundaries"
                @change="handleBoundariesChange"
                active-text="行政边界"
                inactive-text="边界隐藏"
                size="small"
              />
            </div>
            <!-- 3D图层控制 - 仅在3D模式下显示 -->
            <div class="control-item" v-if="mapMode === '3D'">
              <el-switch
                v-model="show3DLayer"
                @change="handle3DLayerChange"
                active-text="3D模型图层"
                inactive-text="3D图层隐藏"
                size="small"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 天气热力图控制区域 -->
      <div class="control-section weather-heatmap-section">
        <div class="section-header" @click="toggleWeatherHeatmap">
          <el-icon class="section-icon"><Sunny /></el-icon>
          <span class="section-title">天气热力图</span>
          <el-icon class="expand-icon" :class="{ 'expanded': weatherHeatmapVisible }">
            <ArrowDown />
          </el-icon>
        </div>
        <div class="section-content" v-show="weatherHeatmapVisible">
          <div class="weather-controls">
            <!-- 热力图类型选择 -->
            <div class="control-item heatmap-type-selector">
              <span class="control-label">热力图类型:</span>
              <el-radio-group
                v-model="heatmapType"
                @change="handleHeatmapTypeChange"
                size="small"
                :disabled="weatherLoading"
              >
                <el-radio-button label="temperature">
                  <el-icon><Sunny /></el-icon>
                  气温
                </el-radio-button>
                <el-radio-button label="precipitation">
                  <el-icon><Cloudy /></el-icon>
                  降水量
                </el-radio-button>
              </el-radio-group>
            </div>

            <!-- 热力图显示开关 -->
            <div class="control-item">
              <el-switch
                v-model="showWeatherHeatmap"
                @change="handleWeatherHeatmapChange"
                :active-text="`${heatmapType === 'temperature' ? '气温' : '降水量'}热力图显示`"
                :inactive-text="`${heatmapType === 'temperature' ? '气温' : '降水量'}热力图隐藏`"
                size="small"
                :disabled="weatherLoading"
              />
            </div>

            <!-- 数据刷新按钮 -->
            <div class="weather-actions">
              <el-button
                size="small"
                type="primary"
                :icon="Refresh"
                @click="handleWeatherDataRefresh"
                :loading="weatherLoading"
                :disabled="weatherLoading"
                class="weather-refresh-btn"
              >
                {{ weatherLoading ? '更新中...' : '刷新模拟数据' }}
              </el-button>
            </div>

            <!-- 统计信息显示 -->
            <div v-if="heatmapType === 'temperature' && weatherStats" class="weather-stats">
              <div class="stats-item">
                <span class="stats-label">数据点:</span>
                <span class="stats-value">{{ weatherStats.totalPlaces }}个</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">温度范围:</span>
                <span class="stats-value">{{ weatherStats.minTemperature }}°C ~ {{ weatherStats.maxTemperature }}°C</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">平均温度:</span>
                <span class="stats-value">{{ weatherStats.averageTemperature }}°C</span>
              </div>
              <div v-if="weatherStats.lastUpdateTime" class="stats-item">
                <span class="stats-label">更新时间:</span>
                <span class="stats-value">{{ formatUpdateTime(weatherStats.lastUpdateTime) }}</span>
              </div>
            </div>

            <!-- 降水量统计信息显示 -->
            <div v-if="heatmapType === 'precipitation' && precipitationStats" class="weather-stats">
              <div class="stats-item">
                <span class="stats-label">数据点:</span>
                <span class="stats-value">{{ precipitationStats.totalPlaces }}个</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">降水量范围:</span>
                <span class="stats-value">{{ precipitationStats.minPrecipitation }}mm ~ {{ precipitationStats.maxPrecipitation }}mm</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">平均降水量:</span>
                <span class="stats-value">{{ precipitationStats.averagePrecipitation }}mm</span>
              </div>
              <div v-if="precipitationStats.lastUpdateTime" class="stats-item">
                <span class="stats-label">更新时间:</span>
                <span class="stats-value">{{ formatUpdateTime(precipitationStats.lastUpdateTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图例说明区域 -->
      <div class="control-section legend-section">
        <div class="section-header" @click="toggleLegend">
          <el-icon class="section-icon"><Document /></el-icon>
          <span class="section-title">图例说明</span>
          <el-icon class="expand-icon" :class="{ 'expanded': legendVisible }">
            <ArrowDown />
          </el-icon>
        </div>
        <div class="section-content legend-content" v-show="legendVisible">

          <!-- 详细模式 -->
          <div v-if="panelMode === 'detailed'" class="detailed-mode">
          <!-- 电压等级图例 -->
          <div class="legend-group">
            <div class="legend-group-header">
              <h4 class="legend-group-title">电压等级</h4>
              <div class="filter-actions">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="selectAllVoltages"
                >
                  全选
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="clearAllVoltages"
                >
                  清空
                </el-button>
              </div>
            </div>
            <div class="legend-items">
              <div class="legend-item" v-for="voltage in voltageTypes" :key="voltage.level">
                <el-checkbox
                  v-model="activeVoltageFilters"
                  :label="voltage.level"
                  class="legend-checkbox"
                  @change="() => handleVoltageFilterChange(activeVoltageFilters)"
                >
                  <div class="legend-item-content">
                    <div
                      class="legend-line"
                      :style="{
                        height: voltage.strokeWeight + 'px',
                        backgroundColor: voltage.color
                      }"
                    ></div>
                    <span class="legend-label">{{ voltage.level }}</span>
                  </div>
                </el-checkbox>
              </div>
            </div>
          </div>

          <!-- 线路状态图例 -->
          <div class="legend-group">
            <div class="legend-group-header">
              <h4 class="legend-group-title">线路状态</h4>
              <div class="filter-actions">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="selectAllStatuses"
                >
                  全选
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="clearAllStatuses"
                >
                  清空
                </el-button>
              </div>
            </div>
            <div class="legend-items">
              <div class="legend-item" v-for="status in statusTypes" :key="status.name">
                <el-checkbox
                  v-model="activeFilters"
                  :label="status.name"
                  class="legend-checkbox"
                  @change="() => handleFilterChange(activeFilters)"
                >
                  <div class="legend-item-content">
                    <div
                      class="legend-line status-line"
                      :class="status.className"
                    ></div>
                    <span class="legend-label">{{ status.description }}（{{ status.name }}）</span>
                  </div>
                </el-checkbox>
              </div>
            </div>
          </div>

          <!-- 变电站类型图例 -->
          <div class="legend-group">
            <div class="legend-group-header">
              <h4 class="legend-group-title">变电站类型</h4>
              <div class="filter-actions">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="selectAllSubstations"
                >
                  全选
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="clearAllSubstations"
                >
                  清空
                </el-button>
              </div>
            </div>
            <div class="legend-items">
              <div class="legend-item" v-for="(substation, key) in substationTypes" :key="key">
                <el-checkbox
                  v-model="activeSubstationFilters"
                  :label="key"
                  class="legend-checkbox"
                  @change="() => handleSubstationFilterChange(activeSubstationFilters)"
                >
                  <div class="legend-item-content">
                    <div class="legend-facility-icon">
                      <img
                        :src="`/src/assets/icon/${substation.icon}.svg`"
                        :alt="substation.name"
                        class="facility-icon-img"
                        :style="{
                          filter: `hue-rotate(${getHueRotation(substation.color)}deg)`
                        }"
                      />
                    </div>
                    <span class="legend-label">{{ substation.name }}</span>
                  </div>
                </el-checkbox>
              </div>
            </div>
          </div>

          <!-- 发电站类型图例 -->
          <div class="legend-group">
            <div class="legend-group-header">
              <h4 class="legend-group-title">发电站类型</h4>
              <div class="filter-actions">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="selectAllPowerPlants"
                >
                  全选
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="clearAllPowerPlants"
                >
                  清空
                </el-button>
              </div>
            </div>
            <div class="legend-items">
              <div class="legend-item" v-for="(plant, key) in powerPlantTypes" :key="key">
                <el-checkbox
                  v-model="activePowerPlantFilters"
                  :label="key"
                  class="legend-checkbox"
                  @change="() => handlePowerPlantFilterChange(activePowerPlantFilters)"
                >
                  <div class="legend-item-content">
                    <div class="legend-facility-icon">
                      <img
                        :src="`/src/assets/icon/${plant.icon}.svg`"
                        :alt="plant.name"
                        class="facility-icon-img"
                        :style="{
                          filter: `hue-rotate(${getHueRotation(plant.color)}deg)`
                        }"
                      />
                    </div>
                    <span class="legend-label">{{ plant.name }}</span>
                  </div>
                </el-checkbox>
              </div>
            </div>
          </div>

          <!-- 电塔类型图例 -->
          <div class="legend-group">
            <div class="legend-group-header">
              <h4 class="legend-group-title">电塔</h4>
              <div class="filter-actions">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="selectAllTowers"
                >
                  全选
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="clearAllTowers"
                >
                  清空
                </el-button>
              </div>
            </div>
            <div class="legend-items">
              <div class="legend-item" v-for="(tower, key) in towerTypes" :key="key">
                <el-checkbox
                  v-model="activeTowerFilters"
                  :label="key"
                  class="legend-checkbox"
                  @change="() => handleTowerFilterChange(activeTowerFilters)"
                >
                  <div class="legend-item-content">
                    <div class="legend-facility-icon">
                      <img
                        :src="`/src/assets/icon/${tower.icon}.svg`"
                        :alt="tower.name"
                        class="facility-icon-img"
                        :style="{
                          filter: `hue-rotate(${getHueRotation(tower.color)}deg)`
                        }"
                      />
                    </div>
                    <span class="legend-label">{{ tower.name }}</span>
                  </div>
                </el-checkbox>
              </div>
            </div>
          </div>
          </div>

          <!-- 简洁模式 -->
          <div v-else class="compact-mode">
            <!-- 图例显示开关 -->
            <div class="legend-switch-wrapper">
              <div class="compact-group">
                <div class="compact-group-header">
                  <h4 class="compact-group-title">图例显示</h4>
                </div>
                <el-switch
                  v-model="showRightLegend"
                  size="small"
                  active-text="显示"
                  inactive-text="隐藏"
                  @change="handleLegendToggle"
                />
              </div>
            </div>

            <!-- 电塔显示开关（置顶） -->
            <div class="compact-group">
              <div class="compact-group-header">
                <h4 class="compact-group-title">电塔</h4>
              </div>
              <div class="tower-switch-wrapper">
                <el-switch
                  v-model="showTowers"
                  @change="handleTowerSwitchChange"
                  active-text="显示"
                  inactive-text="隐藏"
                  size="small"
                  class="tower-switch"
                />
                <div class="tower-info">
                  <div class="tower-icon-wrapper">
                    <img
                      src="/src/assets/icon/tower.svg"
                      alt="电塔"
                      class="tower-icon-img"
                      :style="{
                        filter: `hue-rotate(${getHueRotation('#1890ff')}deg)`
                      }"
                    />
                  </div>
                  <span class="tower-label">电塔</span>
                </div>
              </div>
            </div>

            <!-- 电压等级下拉选择 -->
            <div class="compact-group">
              <div class="compact-group-header">
                <h4 class="compact-group-title">电压等级</h4>
              </div>
              <el-select
                v-model="activeVoltageFilters"
                multiple
                placeholder="选择电压等级"
                size="small"
                class="compact-select"
                @change="handleVoltageFilterChange"
              >
                <el-option
                  v-for="voltage in voltageTypes"
                  :key="voltage.level"
                  :label="voltage.level"
                  :value="voltage.level"
                >
                  <div class="option-content">
                    <div
                      class="option-line"
                      :style="{
                        height: '3px',
                        backgroundColor: voltage.color,
                        width: '20px',
                        marginRight: '8px'
                      }"
                    ></div>
                    <span>{{ voltage.level }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>

            <!-- 线路状态下拉选择 -->
            <div class="compact-group">
              <div class="compact-group-header">
                <h4 class="compact-group-title">线路状态</h4>
              </div>
              <el-select
                v-model="activeFilters"
                multiple
                placeholder="选择线路状态"
                size="small"
                class="compact-select"
                @change="handleFilterChange"
              >
                <el-option
                  v-for="filter in filterOptions"
                  :key="filter.value"
                  :label="filter.label"
                  :value="filter.value"
                >
                  <div class="option-content">
                    <el-icon :class="filter.iconClass" class="option-icon">
                      <component :is="filter.icon" />
                    </el-icon>
                    <span>{{ filter.label }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>

            <!-- 变电站类型下拉选择 -->
            <div class="compact-group">
              <div class="compact-group-header">
                <h4 class="compact-group-title">变电站类型</h4>
              </div>
              <el-select
                v-model="activeSubstationFilters"
                multiple
                placeholder="选择变电站类型"
                size="small"
                class="compact-select"
                @change="handleSubstationFilterChange"
              >
                <el-option
                  v-for="(substation, key) in substationTypes"
                  :key="key"
                  :label="substation.name"
                  :value="key"
                >
                  <div class="option-content">
                    <div class="option-icon-wrapper">
                      <img
                        :src="`/src/assets/icon/${substation.icon}.svg`"
                        :alt="substation.name"
                        class="option-icon-img"
                        :style="{
                          filter: `hue-rotate(${getHueRotation(substation.color)}deg)`
                        }"
                      />
                    </div>
                    <span>{{ substation.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>

            <!-- 发电站类型下拉选择 -->
            <div class="compact-group">
              <div class="compact-group-header">
                <h4 class="compact-group-title">发电站类型</h4>
              </div>
              <el-select
                v-model="activePowerPlantFilters"
                multiple
                placeholder="选择发电站类型"
                size="small"
                class="compact-select"
                @change="handlePowerPlantFilterChange"
              >
                <el-option
                  v-for="(plant, key) in powerPlantTypes"
                  :key="key"
                  :label="plant.name"
                  :value="key"
                >
                  <div class="option-content">
                    <div class="option-icon-wrapper">
                      <img
                        :src="`/src/assets/icon/${plant.icon}.svg`"
                        :alt="plant.name"
                        class="option-icon-img"
                        :style="{
                          filter: `hue-rotate(${getHueRotation(plant.color)}deg)`
                        }"
                      />
                    </div>
                    <span>{{ plant.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>



            <!-- 线路级别下拉选择 -->
            <div class="compact-group">
              <div class="compact-group-header">
                <h4 class="compact-group-title">线路级别</h4>
              </div>
              <el-select
                v-model="activeLevelFilters"
                multiple
                placeholder="选择线路级别"
                size="small"
                class="compact-select"
                @change="handleLevelFilterChange"
              >
                <el-option
                  v-for="(level, key) in lineLevelTypes"
                  :key="key"
                  :label="level.name"
                  :value="key"
                >
                  <div class="option-content">
                    <div
                      class="option-badge"
                      :style="{
                        backgroundColor: level.color,
                        color: '#fff',
                        padding: '2px 6px',
                        borderRadius: '3px',
                        fontSize: '10px',
                        marginRight: '8px'
                      }"
                    >
                      {{ level.badge }}
                    </div>
                    <span>{{ level.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>
          </div>

        </div>
      </div>



      <!-- 线路级别筛选区域 (仅在详细模式下显示) -->
      <div v-if="panelMode === 'detailed'" class="control-section line-level-section">
        <div class="section-header" @click="toggleLineLevel">
          <el-icon class="section-icon"><Check /></el-icon>
          <span class="section-title">线路级别</span>
          <el-icon class="expand-icon" :class="{ 'expanded': lineLevelVisible }">
            <ArrowDown />
          </el-icon>
        </div>
        <div class="section-content" v-show="lineLevelVisible">
          <div class="level-filter-group">
            <div class="filter-actions">
              <el-button
                size="small"
                type="primary"
                text
                @click="selectAllLevels"
              >
                全选
              </el-button>
              <el-button
                size="small"
                type="primary"
                text
                @click="clearAllLevels"
              >
                清空
              </el-button>
            </div>
            <div class="level-items">
              <div class="level-item" v-for="(level, key) in lineLevelTypes" :key="key">
                <el-checkbox
                  v-model="activeLevelFilters"
                  :label="key"
                  class="level-checkbox"
                  @change="() => handleLevelFilterChange(activeLevelFilters)"
                >
                  <div class="level-item-content">
                    <div
                      class="level-badge"
                      :style="{
                        backgroundColor: level.color,
                        color: '#fff'
                      }"
                    >
                      {{ level.badge }}
                    </div>
                    <span class="level-label">{{ level.name }}</span>
                  </div>
                </el-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- 右下角图例面板 -->
  <div v-if="showRightLegend" class="right-legend-panel">
    <div class="legend-panel-header">
      <h3 class="legend-panel-title">
        <el-icon class="legend-title-icon"><Document /></el-icon>
        图例说明
      </h3>
      <el-button
        class="legend-close-btn"
        :icon="Close"
        size="small"
        text
        @click="showRightLegend = false"
        title="关闭图例"
      />
    </div>

    <div class="legend-panel-content">
      <!-- 电压等级图例 -->
      <div class="right-legend-group">
        <h4 class="right-legend-title">电压等级</h4>
        <div class="right-legend-items">
          <div class="right-legend-item" v-for="voltage in voltageTypes" :key="voltage.level">
            <div
              class="right-legend-line"
              :style="{
                height: voltage.strokeWeight + 'px',
                backgroundColor: voltage.color
              }"
            ></div>
            <span class="right-legend-label">{{ voltage.level }}</span>
          </div>
        </div>
      </div>

      <!-- 线路状态图例 -->
      <div class="right-legend-group">
        <h4 class="right-legend-title">线路状态</h4>
        <div class="right-legend-items">
          <div class="right-legend-item" v-for="filter in filterOptions" :key="filter.value">
            <el-icon :class="filter.iconClass" class="right-legend-icon">
              <component :is="filter.icon" />
            </el-icon>
            <span class="right-legend-label">{{ filter.label }}</span>
          </div>
        </div>
      </div>

      <!-- 变电站类型图例 -->
      <div class="right-legend-group">
        <h4 class="right-legend-title">变电站类型</h4>
        <div class="right-legend-items">
          <div class="right-legend-item" v-for="(substation, key) in substationTypes" :key="key">
            <div class="right-legend-facility-icon">
              <img
                :src="`/src/assets/icon/${substation.icon}.svg`"
                :alt="substation.name"
                class="right-facility-icon-img"
                :style="{
                  filter: `hue-rotate(${getHueRotation(substation.color)}deg)`
                }"
              />
            </div>
            <span class="right-legend-label">{{ substation.name }}</span>
          </div>
        </div>
      </div>

      <!-- 发电站类型图例 -->
      <div class="right-legend-group">
        <h4 class="right-legend-title">发电站类型</h4>
        <div class="right-legend-items">
          <div class="right-legend-item" v-for="(plant, key) in powerPlantTypes" :key="key">
            <div class="right-legend-facility-icon">
              <img
                :src="`/src/assets/icon/${plant.icon}.svg`"
                :alt="plant.name"
                class="right-facility-icon-img"
                :style="{
                  filter: `hue-rotate(${getHueRotation(plant.color)}deg)`
                }"
              />
            </div>
            <span class="right-legend-label">{{ plant.name }}</span>
          </div>
        </div>
      </div>

      <!-- 电塔类型图例 -->
      <div class="right-legend-group">
        <h4 class="right-legend-title">电塔</h4>
        <div class="right-legend-items">
          <div class="right-legend-item" v-for="(tower, key) in towerTypes" :key="key">
            <div class="right-legend-facility-icon">
              <img
                :src="`/src/assets/icon/${tower.icon}.svg`"
                :alt="tower.name"
                class="right-facility-icon-img"
                :style="{
                  filter: `hue-rotate(${getHueRotation(tower.color)}deg)`
                }"
              />
            </div>
            <span class="right-legend-label">{{ tower.name }}</span>
          </div>
        </div>
      </div>

      <!-- 线路级别图例 -->
      <div class="right-legend-group">
        <h4 class="right-legend-title">线路级别</h4>
        <div class="right-legend-items">
          <div class="right-legend-item" v-for="(level, key) in lineLevelTypes" :key="key">
            <div
              class="right-level-badge"
              :style="{
                backgroundColor: level.color,
                color: '#fff'
              }"
            >
              {{ level.badge }}
            </div>
            <span class="right-legend-label">{{ level.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import {
  Setting,
  Expand,
  Fold,
  View,
  Grid,
  Box,
  Document,
  ArrowDown,
  Check,
  Clock,
  Edit,
  Loading,
  Position,
  Monitor,
  Close,
  Sunny,
  Refresh,
  Cloudy
} from '@element-plus/icons-vue'
import { FACILITY_STYLES, PROJECT_IMPORTANCE_CONFIG } from '../data/powerConfig.js'

// 响应式数据
const isCollapsed = ref(false)
const panelMode = ref('detailed') // 面板模式：detailed(详细模式) | compact(简洁模式)
const mapMode = ref('2D')
const switchingMode = ref(false)
const mapLayer = ref('normal') // 地图图层类型：normal(普通) | satellite(卫星)
const switchingLayer = ref(false)
const legendVisible = ref(true)

// 新增控制区域的可见性
const mapDisplayVisible = ref(true)
const lineLevelVisible = ref(true)
const weatherHeatmapVisible = ref(true)

// 图例弹窗控制
const showLegendDialog = ref(false)

// 右下角图例面板控制
const showRightLegend = ref(false)

// 地名和边界控制
const showPlaceNames = ref(false)
const showBoundaries = ref(true) // 初始化时显示边界，与地图初始状态保持一致

// 3D图层控制
const show3DLayer = ref(true) // 3D模式下默认显示3D图层

// 天气热力图控制
const showWeatherHeatmap = ref(false)
const heatmapType = ref('temperature') // 热力图类型：temperature(气温) | precipitation(降水量)
const weatherLoading = ref(false)
const weatherStats = ref(null)
const precipitationStats = ref(null)

// 线路级别筛选状态 - 默认全选（包括"其他"）
const activeLevelFilters = ref(['国际', '国重', '省重', '战略', '应急', '民生', '其他'])

const activeFilters = ref(['已完成', '建设中', '规划中'])

// 设施筛选状态
const activeSubstationFilters = ref(['substation', 'converter_station'])
const activePowerPlantFilters = ref(['thermal', 'hydro', 'wind', 'solar'])
const activeTowerFilters = ref(['tower'])

// 电塔显示开关状态
const showTowers = ref(true)

// 电压等级筛选状态
const activeVoltageFilters = ref(['500KV', '220KV', '110KV', '35KV', '10KV'])

// 电压等级配置 - 与powerConfig.js保持一致
const voltageTypes = reactive([
  { level: '500KV', strokeWeight: 8, color: '#1890ff' }, // 蓝色
  { level: '220KV', strokeWeight: 6, color: '#1890ff' }, // 蓝色
  { level: '110KV', strokeWeight: 4, color: '#1890ff' }, // 蓝色
  { level: '35KV', strokeWeight: 3, color: '#1890ff' },  // 蓝色
  { level: '10KV', strokeWeight: 2, color: '#1890ff' }   // 蓝色
])

// 线路状态配置 - 与powerConfig.js保持一致
const statusTypes = reactive([
  { name: '已完成', className: 'solid-line', description: '实线', color: '#1890ff' },
  { name: '建设中', className: 'dashed-line', description: '虚线', color: '#1890ff' },
  { name: '规划中', className: 'dotted-line', description: '点线', color: '#1890ff' }
])

// 筛选选项配置
const filterOptions = reactive([
  { 
    value: '已完成', 
    label: '已完成', 
    icon: Check, 
    iconClass: 'completed-icon' 
  },
  { 
    value: '建设中', 
    label: '建设中', 
    icon: Clock, 
    iconClass: 'building-icon' 
  },
  { 
    value: '规划中', 
    label: '规划中', 
    icon: Edit, 
    iconClass: 'planning-icon' 
  }
])

// 设施类型配置
const substationTypes = reactive(FACILITY_STYLES.substationTypes)
const powerPlantTypes = reactive(FACILITY_STYLES.powerPlantTypes)
const towerTypes = reactive(FACILITY_STYLES.towerTypes)

// 线路级别类型配置
const lineLevelTypes = reactive(PROJECT_IMPORTANCE_CONFIG)

// 事件定义
const emit = defineEmits(['mapModeChange', 'mapLayerChange', 'filterChange', 'facilityFilterChange', 'voltageFilterChange', 'placeNamesChange', 'boundariesChange', 'levelFilterChange', 'weatherHeatmapChange', 'weatherDataRefresh', 'threeDLayerChange'])

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

const togglePanelMode = () => {
  panelMode.value = panelMode.value === 'detailed' ? 'compact' : 'detailed'
  console.log('面板模式切换到:', panelMode.value)
}

const toggleLegend = () => {
  legendVisible.value = !legendVisible.value
}

const handleMapModeChange = async (mode) => {
  try {
    console.log('地图模式切换:', mode)

    // 设置切换状态
    switchingMode.value = true

    // 发送切换事件到父组件
    emit('mapModeChange', mode)

    // 模拟切换延迟，给用户视觉反馈
    setTimeout(() => {
      switchingMode.value = false
      console.log(`地图模式已切换到: ${mode}`)
    }, 800)

  } catch (error) {
    console.error('地图模式切换失败:', error)
    switchingMode.value = false

    // 如果切换失败，恢复到之前的模式
    // 这里可以添加错误提示
  }
}

const handleMapLayerChange = async (layer) => {
  try {
    console.log('地图图层切换:', layer)

    // 设置切换状态
    switchingLayer.value = true

    // 发送切换事件到父组件
    emit('mapLayerChange', layer)

    // 模拟切换延迟，给用户视觉反馈
    setTimeout(() => {
      switchingLayer.value = false
      console.log(`地图图层已切换到: ${layer}`)
    }, 600)

  } catch (error) {
    console.error('地图图层切换失败:', error)
    switchingLayer.value = false

    // 如果切换失败，恢复到之前的图层
    // 这里可以添加错误提示
  }
}

const handleFilterChange = (filters) => {
  console.log('筛选条件变更:', filters)
  emit('filterChange', filters)
}



const handleSubstationFilterChange = (filters) => {
  console.log('变电站筛选变化:', filters)
  emit('facilityFilterChange', {
    type: 'substations',
    filters: filters
  })
}

const handlePowerPlantFilterChange = (filters) => {
  console.log('发电站筛选变化:', filters)
  emit('facilityFilterChange', {
    type: 'powerPlants',
    filters: filters
  })
}

const selectAllSubstations = () => {
  activeSubstationFilters.value = Object.keys(substationTypes)
  handleSubstationFilterChange(activeSubstationFilters.value)
}

const clearAllSubstations = () => {
  activeSubstationFilters.value = []
  handleSubstationFilterChange(activeSubstationFilters.value)
}

const selectAllPowerPlants = () => {
  activePowerPlantFilters.value = Object.keys(powerPlantTypes)
  handlePowerPlantFilterChange(activePowerPlantFilters.value)
}

const clearAllPowerPlants = () => {
  activePowerPlantFilters.value = []
  handlePowerPlantFilterChange(activePowerPlantFilters.value)
}

// 电塔筛选方法
const handleTowerFilterChange = (filters) => {
  console.log('电塔筛选变化:', filters)
  emit('facilityFilterChange', {
    type: 'towers',
    filters: filters
  })
}

const selectAllTowers = () => {
  activeTowerFilters.value = Object.keys(towerTypes)
  handleTowerFilterChange(activeTowerFilters.value)
}

const clearAllTowers = () => {
  activeTowerFilters.value = []
  handleTowerFilterChange(activeTowerFilters.value)
}

// 电塔开关处理方法
const handleTowerSwitchChange = (value) => {
  console.log('电塔显示状态变化:', value)
  if (value) {
    // 开启时，显示电塔
    activeTowerFilters.value = ['tower']
  } else {
    // 关闭时，隐藏电塔
    activeTowerFilters.value = []
  }
  handleTowerFilterChange(activeTowerFilters.value)
}

// 电压等级筛选方法
const handleVoltageFilterChange = (filters) => {
  console.log('电压等级筛选变化:', filters)
  emit('voltageFilterChange', filters)
}

const selectAllVoltages = () => {
  activeVoltageFilters.value = voltageTypes.map(voltage => voltage.level)
  handleVoltageFilterChange(activeVoltageFilters.value)
}

const clearAllVoltages = () => {
  activeVoltageFilters.value = []
  handleVoltageFilterChange(activeVoltageFilters.value)
}

// 线路状态筛选方法
const selectAllStatuses = () => {
  activeFilters.value = filterOptions.map(option => option.value)
  handleFilterChange(activeFilters.value)
}

const clearAllStatuses = () => {
  activeFilters.value = []
  handleFilterChange(activeFilters.value)
}

// 新增控制区域的切换方法
const toggleMapDisplay = () => {
  mapDisplayVisible.value = !mapDisplayVisible.value
}

const toggleLineLevel = () => {
  lineLevelVisible.value = !lineLevelVisible.value
}

const toggleWeatherHeatmap = () => {
  weatherHeatmapVisible.value = !weatherHeatmapVisible.value
}

// 地名和边界控制方法
const handlePlaceNamesChange = (value) => {
  console.log('地名显示变化:', value)
  emit('placeNamesChange', value)
}

const handleBoundariesChange = (value) => {
  console.log('行政边界显示变化:', value)
  emit('boundariesChange', value)
}

const handle3DLayerChange = (value) => {
  console.log('3D图层显示变化:', value)
  emit('threeDLayerChange', value)
}

// 天气热力图控制方法
const handleHeatmapTypeChange = (type) => {
  console.log('热力图类型变化:', type)
  emit('heatmapTypeChange', type)

  // 如果当前热力图是显示状态，需要切换到新类型
  if (showWeatherHeatmap.value) {
    emit('heatmapVisibilityChange', true, type)
  }
}

const handleWeatherHeatmapChange = (value) => {
  console.log(`${heatmapType.value === 'temperature' ? '气温' : '降水量'}热力图显示变化:`, value)
  emit('heatmapVisibilityChange', value, heatmapType.value)
}

const handleWeatherDataRefresh = async () => {
  console.log('刷新天气模拟数据')
  weatherLoading.value = true
  try {
    await emit('weatherDataRefresh')
  } finally {
    weatherLoading.value = false
  }
}

// 格式化更新时间
const formatUpdateTime = (time) => {
  if (!time) return ''
  return time.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 图例弹窗处理方法
const handleLegendDialogClose = (done) => {
  showLegendDialog.value = false
  if (done) done()
}

// 右下角图例切换处理方法
const handleLegendToggle = (value) => {
  console.log('图例显示状态切换:', value)
}

// 线路级别筛选方法
const handleLevelFilterChange = (filters) => {
  console.log('线路级别筛选变化:', filters)
  emit('levelFilterChange', filters)
}

const selectAllLevels = () => {
  activeLevelFilters.value = Object.keys(lineLevelTypes)
  handleLevelFilterChange(activeLevelFilters.value)
}

const clearAllLevels = () => {
  activeLevelFilters.value = []
  handleLevelFilterChange(activeLevelFilters.value)
}

// 颜色转换工具函数
const getHueRotation = (color) => {
  // 简单的颜色到色相转换，用于SVG图标着色
  const colorMap = {
    '#1890ff': 0,    // 蓝色
    '#722ed1': 270,  // 紫色
    '#fa8c16': 30,   // 橙色
    '#52c41a': 120,  // 绿色
    '#ff4d4f': 0,    // 红色
    '#faad14': 45    // 黄色
  }
  return colorMap[color] || 0
}

// 暴露给父组件的方法和数据
defineExpose({
  mapMode,
  switchingMode,
  mapLayer,
  switchingLayer,
  activeFilters,
  activeSubstationFilters,
  activePowerPlantFilters,
  activeTowerFilters,
  activeVoltageFilters,
  showTowers,
  isCollapsed,
  legendVisible,
  showWeatherHeatmap,
  weatherLoading,
  weatherStats
})
</script>

<style scoped>
.control-panel {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  transition: all 0.3s ease;
  max-height: calc(100vh - 40px);
  overflow: hidden;
}

.control-panel.collapsed {
  width: 200px;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mode-switch-btn {
  color: white !important;
  border: none !important;
}

.mode-switch-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.panel-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.title-icon {
  margin-right: 8px;
  font-size: 18px;
}

.collapse-btn {
  color: white !important;
  border: none !important;
}

.collapse-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.panel-content {
  padding: 0;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

.control-section {
  padding: 8px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.control-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
  user-select: none;
  padding: 4px 0;
}

.section-icon {
  margin-right: 8px;
  color: #667eea;
  font-size: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.expand-icon {
  margin-left: auto;
  transition: transform 0.3s ease;
  color: #999;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.section-content {
  margin-left: 16px;
}

/* 地图模式切换样式 */
.mode-radio-group {
  width: 100%;
  margin-bottom: 8px;
}

.mode-radio-group :deep(.el-radio-button) {
  flex: 1;
}

.mode-radio-group :deep(.el-radio-button__inner) {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-size: 13px;
}

.mode-radio-group :deep(.el-radio-button__inner:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.mode-radio-group :deep(.el-radio-button.is-active .el-radio-button__inner) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.mode-radio-group :deep(.el-radio-button.is-disabled .el-radio-button__inner) {
  opacity: 0.6;
  cursor: not-allowed;
}

.mode-button {
  position: relative;
}

.mode-icon {
  font-size: 14px;
  transition: all 0.3s ease;
}

.mode-text {
  font-weight: 500;
  font-size: 12px;
  transition: all 0.3s ease;
}

.mode-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 8px;
  height: 8px;
  background: #67C23A;
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

/* 切换状态指示器 */
.switching-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 4px;
  margin-top: 6px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.switching-icon {
  color: #667eea;
  animation: spin 1s linear infinite;
}

.switching-text {
  font-size: 11px;
  color: #667eea;
  font-weight: 500;
}

/* 模式状态显示 */
.mode-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  font-size: 12px;
}

.status-label {
  color: #666;
  font-weight: 500;
}

.status-value {
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.status-value.mode-2d {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.status-value.mode-3d {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

/* 图例样式 */
.legend-content {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.legend-group {
  margin-bottom: 12px;
}

.legend-group:last-child {
  margin-bottom: 0;
}

.legend-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.legend-group-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-checkbox {
  width: 100%;
  margin: 0;
}

.legend-checkbox .el-checkbox__label {
  width: 100%;
  padding-left: 8px;
}

.legend-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.legend-line {
  width: 40px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-line.status-line {
  height: 3px !important;
}

.legend-line.status-line.solid-line {
  background: #1890ff !important; /* 蓝色 - 已完成 */
}

.legend-line.status-line.dashed-line {
  background: repeating-linear-gradient(
    to right,
    #1890ff 0px,
    #1890ff 15px,
    transparent 15px,
    transparent 25px
  ) !important; /* 蓝色 - 建设中，长虚线 */
}

.legend-line.status-line.dotted-line {
  background: repeating-linear-gradient(
    to right,
    #1890ff 0px,
    #1890ff 3px,
    transparent 3px,
    transparent 15px
  ) !important; /* 蓝色 - 规划中，点线效果 */
}

.legend-label {
  font-size: 12px;
  color: #666;
}

/* 筛选区域样式 */
.filter-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 地图显示控制样式 */
.display-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.control-item :deep(.el-switch) {
  margin-right: 0;
}

.control-item :deep(.el-switch__label) {
  font-size: 13px;
  color: #333;
}

/* 天气热力图控制样式 */
.weather-heatmap-section {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border-radius: 8px;
  margin: 8px 0;
}

.weather-heatmap-section .section-header {
  color: #2d3436;
  font-weight: 600;
}

.weather-heatmap-section .section-icon {
  color: #e17055;
}

.weather-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.weather-actions {
  display: flex;
  justify-content: center;
}

.weather-refresh-btn {
  width: 100%;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border: none;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.weather-refresh-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4);
}

.weather-refresh-btn:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.weather-stats {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  padding: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
}

.stats-item:last-child {
  margin-bottom: 0;
}

.stats-label {
  color: #666;
  font-weight: 500;
}

.stats-value {
  color: #333;
  font-weight: 600;
}

/* 线路级别控制样式 */
.level-filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.level-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.level-item {
  display: flex;
  align-items: center;
}

.level-checkbox {
  width: 100%;
  margin: 0;
}

.level-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.level-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  text-align: center;
  min-width: 24px;
  flex-shrink: 0;
}

.level-label {
  font-size: 12px;
  color: #333;
}

/* 简洁模式样式 */
.compact-mode {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.compact-group {
  margin-bottom: 12px;
}

.compact-group-header {
  margin-bottom: 6px;
}

.compact-group-title {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.compact-select {
  width: 100%;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-icon-wrapper {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e8e8e8;
}

.option-icon-img {
  width: 12px;
  height: 12px;
  object-fit: contain;
}

.compact-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.compact-checkbox {
  margin-right: 0;
}

.checkbox-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.checkbox-icon-wrapper {
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e8e8e8;
}

.checkbox-icon-img {
  width: 10px;
  height: 10px;
  object-fit: contain;
}

/* 电塔开关样式 */
.tower-switch-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  gap: 12px;
}

.tower-switch {
  flex-shrink: 0;
}

.tower-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.tower-icon-wrapper {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.tower-icon-img {
  width: 14px;
  height: 14px;
  object-fit: contain;
}

.tower-label {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

/* 图例开关样式 */
.legend-switch-wrapper {
  margin-bottom: 16px;
}

.legend-switch-wrapper .compact-group {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
}

.legend-switch-wrapper .compact-group-header {
  margin-bottom: 8px;
}

.legend-switch-wrapper .compact-group-title {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

/* 图例弹窗按钮样式 */
.legend-popup-button-wrapper {
  margin-bottom: 16px;
  text-align: center;
}

.legend-popup-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 右下角图例面板样式 */
.right-legend-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 320px;
  max-height: calc(100vh - 120px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 999;
  overflow: hidden;
}

.legend-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.legend-panel-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-title-icon {
  font-size: 18px;
}

.legend-close-btn {
  color: white !important;
  border: none !important;
  background: transparent !important;
}

.legend-close-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.legend-panel-content {
  padding: 16px 20px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 图例弹窗样式 */
.legend-dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 右下角图例组样式 */
.right-legend-group {
  margin-bottom: 20px;
}

.right-legend-group:last-child {
  margin-bottom: 0;
}

.right-legend-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
  padding-bottom: 6px;
  border-bottom: 2px solid #409eff;
}

.right-legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.right-legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  font-size: 12px;
}

.right-legend-line {
  width: 30px;
  border-radius: 2px;
  flex-shrink: 0;
}

.right-legend-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.right-legend-facility-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.right-facility-icon-img {
  width: 14px;
  height: 14px;
  object-fit: contain;
}

.right-level-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  flex-shrink: 0;
}

.right-legend-label {
  font-size: 12px;
  color: #333;
  line-height: 1.2;
}

.dialog-legend-group {
  margin-bottom: 24px;
}

.dialog-legend-group:last-child {
  margin-bottom: 0;
}

.dialog-legend-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.dialog-legend-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.dialog-legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.dialog-legend-line {
  width: 40px;
  border-radius: 2px;
  flex-shrink: 0;
}

.dialog-legend-line.status-line {
  height: 3px !important;
}

.dialog-legend-line.status-line.solid-line {
  background: #1890ff !important;
}

.dialog-legend-line.status-line.dashed-line {
  background: repeating-linear-gradient(
    to right,
    #1890ff 0px,
    #1890ff 8px,
    transparent 8px,
    transparent 16px
  ) !important;
  height: 3px !important;
}

.dialog-legend-line.status-line.dotted-line {
  background: repeating-linear-gradient(
    to right,
    #1890ff 0px,
    #1890ff 4px,
    transparent 4px,
    transparent 8px
  ) !important;
  height: 3px !important;
}

.dialog-legend-facility-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.dialog-facility-icon-img {
  width: 18px;
  height: 18px;
  object-fit: contain;
}

.dialog-level-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  min-width: 32px;
  flex-shrink: 0;
}

.dialog-legend-label {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.compact-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.compact-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.compact-group-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.compact-select {
  width: 100%;
}

.compact-select :deep(.el-select__tags) {
  max-height: 60px;
  overflow-y: auto;
}

.option-content {
  display: flex;
  align-items: center;
}

.option-line {
  flex-shrink: 0;
}

.option-icon {
  margin-right: 8px;
  font-size: 14px;
}

.option-badge {
  flex-shrink: 0;
}

.filter-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.legend-group .filter-actions {
  flex-shrink: 0;
}

.legend-group .filter-actions .el-button {
  font-size: 11px;
  padding: 2px 6px;
  height: auto;
  min-height: 20px;
}

.filter-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-checkbox {
  margin: 0 !important;
}

.filter-checkbox :deep(.el-checkbox__label) {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #333;
}

.filter-icon {
  font-size: 14px;
}

.filter-icon.completed-icon {
  color: #1890ff; /* 蓝色 - 已完成 */
}

.filter-icon.building-icon {
  color: #1890ff; /* 蓝色 - 建设中 */
}

.filter-icon.planning-icon {
  color: #1890ff; /* 蓝色 - 规划中 */
}

/* 地图图层切换样式 */
.layer-radio-group {
  display: flex;
  flex-direction: row;
  gap: 8px;
  width: 100%;
}

.layer-button {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
  font-size: 13px;
  min-height: 40px;
}

.layer-button:hover {
  border-color: #409eff;
  background: #f0f9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.layer-button.is-active {
  border-color: #409eff;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.layer-icon {
  font-size: 16px;
  color: #606266;
  transition: all 0.3s ease;
}

.layer-button.is-active .layer-icon {
  color: white;
}

.layer-text {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  transition: all 0.3s ease;
}

.layer-button.is-active .layer-text {
  color: white;
}

.layer-indicator {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 8px;
  height: 8px;
  background: #67c23a;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.layer-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 12px;
}

.layer-status .status-value.layer-satellite {
  color: #409eff;
  font-weight: 600;
}

.layer-status .status-value.layer-normal {
  color: #67c23a;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    width: calc(100vw - 40px);
    max-width: 320px;
  }

  .control-panel.collapsed {
    width: 180px;
  }

  .panel-header {
    padding: 12px 16px;
  }

  .control-section {
    padding: 16px;
  }

  .section-content {
    margin-left: 20px;
  }

  /* 小屏幕下地图图层按钮调整 */
  .layer-button {
    padding: 6px 8px;
    font-size: 12px;
    min-height: 36px;
  }

  .layer-icon {
    font-size: 14px;
  }

  .layer-text {
    font-size: 12px;
  }
}

/* 右下角图例面板响应式设计 */
@media (max-width: 768px) {
  .right-legend-panel {
    bottom: 10px;
    right: 10px;
    width: calc(100vw - 20px);
    max-width: 300px;
  }

  .legend-panel-content {
    max-height: calc(100vh - 250px);
  }
}

@media (max-width: 480px) {
  .control-panel {
    top: 10px;
    left: 10px;
    width: calc(100vw - 20px);
    max-width: none;
  }

  .control-panel.collapsed {
    width: 160px;
  }

  .right-legend-panel {
    bottom: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    max-width: none;
  }

  .legend-panel-content {
    max-height: calc(100vh - 300px);
    padding: 12px 16px;
  }

  .right-legend-item {
    padding: 4px 6px;
    font-size: 11px;
  }

  .right-legend-title {
    font-size: 13px;
  }

  /* 超小屏幕下地图图层按钮进一步调整 */
  .layer-radio-group {
    gap: 6px;
  }

  .layer-button {
    padding: 6px;
    font-size: 11px;
    min-height: 32px;
    gap: 4px;
  }

  .layer-icon {
    font-size: 12px;
  }

  .layer-text {
    font-size: 11px;
  }

  .layer-indicator {
    width: 6px;
    height: 6px;
    top: 4px;
    right: 4px;
  }
}

/* 设施图例样式 */
.legend-facility-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e8e8e8;
}

.facility-icon-img {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

/* 设施筛选样式 */
.facility-filter-section {
  border-top: 1px solid #f0f0f0;
}

.filter-group {
  margin-bottom: 16px;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-group-title {
  font-size: 13px;
  font-weight: 600;
  color: #666;
  margin: 0 0 8px 0;
  padding: 0;
}

.facility-checkbox {
  margin: 0 !important;
}

.facility-checkbox :deep(.el-checkbox__label) {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #333;
}

.facility-filter-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e8e8e8;
}

.facility-icon-small {
  width: 12px;
  height: 12px;
  object-fit: contain;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 4px;
}

.panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 地图显示控制样式 */
.map-display-section {
  border-top: 1px solid #f0f0f0;
}

.display-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.control-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.control-item :deep(.el-switch__label) {
  font-size: 13px;
  color: #333;
}

/* 线路级别筛选样式 */
.line-level-section {
  border-top: 1px solid #f0f0f0;
}

.level-filter-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.level-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.level-item {
  display: flex;
  align-items: center;
}

.level-checkbox {
  margin: 0 !important;
  width: 100%;
}

.level-checkbox :deep(.el-checkbox__label) {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #333;
  width: 100%;
}

.level-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.level-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  min-width: 32px;
}

/* 热力图类型选择器样式 */
.heatmap-type-selector {
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.heatmap-type-selector .control-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.heatmap-type-selector :deep(.el-radio-group) {
  width: 100%;
}

.heatmap-type-selector :deep(.el-radio-button) {
  flex: 1;
}

.heatmap-type-selector :deep(.el-radio-button__inner) {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  padding: 8px 12px;
}

.heatmap-type-selector :deep(.el-radio-button__inner .el-icon) {
  font-size: 14px;
}

/* 天气统计信息样式 */
.weather-stats {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-top: 8px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.stats-item:last-child {
  margin-bottom: 0;
}

.stats-label {
  font-size: 12px;
  color: #666;
}

.stats-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.level-label {
  flex: 1;
  font-size: 13px;
  color: #333;
}
</style>
