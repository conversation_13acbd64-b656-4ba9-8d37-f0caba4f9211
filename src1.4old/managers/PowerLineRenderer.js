/**
 * 电力线路渲染器
 * 负责在2D/3D地图上渲染电力线路
 * 支持不同电压等级的样式和状态的线型
 */

import { LINE_STYLES, LINE_STATUS_CONFIG } from '../data/powerConfig.js'

export class PowerLineRenderer {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap

    // 渲染的线路对象存储
    this.renderedLines = new Map() // 按电压等级分组存储
    this.lineObjects = new Map() // 存储实际的地图线路对象

    // 当前激活的筛选条件
    this.activeFilters = ['已完成', '建设中', '规划中'] // 状态筛选
    this.activeVoltageFilters = ['500KV', '220KV', '110KV', '35KV', '10KV'] // 电压等级筛选
    this.activeLevelFilters = ['国际', '国重', '省重', '战略', '应急', '民生', '其他'] // 线路级别筛选 - 默认全选

    // 渲染状态
    this.isRendering = false
    this.renderError = null

    // 事件监听器
    this.eventListeners = new Map()

    console.log('PowerLineRenderer 初始化完成')
  }

  /**
   * 渲染所有线路数据
   * @param {Object} linesData - 按电压等级分组的线路数据
   * @param {Array} filters - 筛选条件
   * @returns {Promise<boolean>} 渲染是否成功
   */
  async renderAllLines(linesData, filters = null) {
    try {
      this.isRendering = true
      this.renderError = null
      
      // 更新筛选条件
      if (filters) {
        this.activeFilters = filters
      }
      
      console.log('开始渲染电力线路，筛选条件:', this.activeFilters)
      
      // 清除现有线路
      this.clearAllLines()
      
      // 按电压等级渲染线路
      for (const [voltageLevel, lines] of Object.entries(linesData)) {
        if (lines && lines.length > 0) {
          await this.renderLinesByVoltage(voltageLevel, lines)
        }
      }
      
      this.isRendering = false
      this.emit('renderComplete', this.getRenderSummary())
      
      console.log('电力线路渲染完成:', this.getRenderSummary())
      return true
      
    } catch (error) {
      this.isRendering = false
      this.renderError = error
      console.error('线路渲染失败:', error)
      this.emit('renderError', error)
      return false
    }
  }

  /**
   * 按电压等级渲染线路
   * @param {string} voltageLevel - 电压等级
   * @param {Array} lines - 线路数组
   */
  async renderLinesByVoltage(voltageLevel, lines) {
    // 检查电压等级是否在筛选范围内
    if (!this.activeVoltageFilters.includes(voltageLevel)) {
      console.log(`${voltageLevel} 电压等级未在筛选范围内，跳过渲染`)
      return
    }

    const filteredLines = this.filterLines(lines)

    if (filteredLines.length === 0) {
      console.log(`${voltageLevel} 电压等级没有符合筛选条件的线路`)
      return
    }

    const renderedObjects = []

    for (const line of filteredLines) {
      try {
        const lineObjects = await this.renderSingleLine(line, voltageLevel)
        renderedObjects.push(...lineObjects)
      } catch (error) {
        console.error(`渲染线路失败 ${line.id}:`, error)
      }
    }

    // 存储渲染结果
    this.renderedLines.set(voltageLevel, {
      lines: filteredLines,
      objects: renderedObjects,
      count: renderedObjects.length,
      lastRendered: new Date()
    })

    console.log(`${voltageLevel} 渲染完成: ${renderedObjects.length} 个线路对象`)
  }

  /**
   * 渲染单条线路
   * @param {Object} line - 线路数据
   * @param {string} voltageLevel - 电压等级
   * @returns {Array} 渲染的线路对象数组
   */
  async renderSingleLine(line, voltageLevel) {
    const lineObjects = []
    
    // 检查是否有分段数据
    if (line.segments && line.segments.length > 0) {
      // 分段渲染
      for (const segment of line.segments) {
        if (this.activeFilters.includes(segment.status)) {
          const segmentObject = this.createLineSegment(line, segment, voltageLevel)
          if (segmentObject) {
            lineObjects.push(segmentObject)
            this.map.add(segmentObject)
          }
        }
      }
    } else {
      // 整条线路渲染
      if (this.activeFilters.includes(line.status)) {
        const lineObject = this.createFullLine(line, voltageLevel)
        if (lineObject) {
          lineObjects.push(lineObject)
          this.map.add(lineObject)
        }
      }
    }
    
    // 存储线路对象引用
    this.lineObjects.set(line.id, lineObjects)
    
    return lineObjects
  }

  /**
   * 创建完整线路对象
   * @param {Object} line - 线路数据
   * @param {string} voltageLevel - 电压等级
   * @returns {Object} 高德地图线路对象
   */
  createFullLine(line, voltageLevel) {
    const style = this.getLineStyle(voltageLevel, line.status)
    
    const polyline = new this.AMap.Polyline({
      path: line.coordinates,
      strokeColor: style.strokeColor,
      strokeWeight: style.strokeWeight,
      strokeOpacity: style.strokeOpacity,
      strokeStyle: style.strokeStyle,
      strokeDasharray: style.strokeDasharray,
      zIndex: style.zIndex,
      cursor: 'pointer',
      // 存储线路数据用于交互
      extData: {
        type: 'powerline',
        lineId: line.id,
        lineName: line.name,
        voltageLevel: voltageLevel,
        status: line.status,
        length: line.length,
        lineLevel: line.lineLevel, // 添加线路级别
        constructionProgress: line.constructionProgress, // 添加建设进度
        segments: line.segments, // 添加分段数据
        description: line.description, // 添加描述
        detailInfo: line.detailInfo
      }
    })
    
    return polyline
  }

  /**
   * 创建线路分段对象
   * @param {Object} line - 线路数据
   * @param {Object} segment - 分段数据
   * @param {string} voltageLevel - 电压等级
   * @returns {Object} 高德地图线路对象
   */
  createLineSegment(line, segment, voltageLevel) {
    const style = this.getLineStyle(voltageLevel, segment.status)
    
    // 获取分段的坐标
    const segmentCoords = line.coordinates.slice(segment.start, segment.end + 1)
    
    const polyline = new this.AMap.Polyline({
      path: segmentCoords,
      strokeColor: style.strokeColor,
      strokeWeight: style.strokeWeight,
      strokeOpacity: style.strokeOpacity,
      strokeStyle: style.strokeStyle,
      strokeDasharray: style.strokeDasharray,
      zIndex: style.zIndex,
      cursor: 'pointer',
      // 存储线路数据用于交互
      extData: {
        type: 'powerline',
        lineId: line.id,
        lineName: line.name,
        voltageLevel: voltageLevel,
        status: segment.status,
        length: line.length,
        lineLevel: line.lineLevel, // 添加线路级别
        constructionProgress: line.constructionProgress, // 添加建设进度
        segments: line.segments, // 添加分段数据
        description: line.description, // 添加描述
        segmentIndex: line.segments.indexOf(segment),
        detailInfo: line.detailInfo
      }
    })
    
    return polyline
  }

  /**
   * 获取线路样式配置
   * @param {string} voltageLevel - 电压等级
   * @param {string} status - 线路状态
   * @returns {Object} 样式配置
   */
  getLineStyle(voltageLevel, status) {
    const voltageStyle = LINE_STYLES.voltageStyles[voltageLevel] || LINE_STYLES.voltageStyles['10KV']
    const statusStyle = LINE_STATUS_CONFIG[status] || LINE_STATUS_CONFIG['已完成']

    // 合并样式 - 使用电压等级的颜色和粗细，状态的线型
    const style = {
      strokeColor: voltageStyle.color, // 使用电压等级的颜色
      strokeWeight: voltageStyle.strokeWeight, // 使用电压等级的粗细
      strokeOpacity: 0.9,
      zIndex: 100 + voltageStyle.priority
    }

    // 设置线型样式 - 高德地图的虚线设置
    if (statusStyle.style === 'dashed') {
      // 虚线：建设中
      style.strokeStyle = 'dashed'
      style.strokeDasharray = [15, 10] // 长虚线
      console.log(`设置虚线样式 - 建设中: ${status}`, style)
    } else if (statusStyle.style === 'dotted') {
      // 点线：规划中 - 使用明显的点线效果
      style.strokeStyle = 'dashed'
      style.strokeDasharray = [4, 16] // 更短的点+更长的空隙
      console.log(`设置点线样式 - 规划中: ${status}`, style)
    } else {
      // 实线：已完成
      style.strokeStyle = 'solid'
      console.log(`设置实线样式 - 已完成: ${status}`, style)
      // 不设置 strokeDasharray，保持实线
    }

    return style
  }

  /**
   * 筛选线路数据
   * @param {Array} lines - 线路数组
   * @returns {Array} 筛选后的线路数组
   */
  filterLines(lines) {
    return lines.filter(line => {
      // 检查线路级别筛选
      // 如果线路没有lineLevel字段或为空，归类到"其他"
      const lineLevel = line.lineLevel || '其他'
      if (!this.activeLevelFilters.includes(lineLevel)) {
        return false
      }

      // 检查线路整体状态
      if (this.activeFilters.includes(line.status)) {
        return true
      }

      // 检查线路分段状态
      if (line.segments) {
        return line.segments.some(segment =>
          this.activeFilters.includes(segment.status)
        )
      }

      return false
    })
  }

  /**
   * 应用新的筛选条件
   * @param {Array} filters - 新的筛选条件
   * @param {Object} linesData - 线路数据
   */
  async applyFilters(filters, linesData) {
    this.activeFilters = filters
    console.log('应用状态筛选条件:', filters)

    // 重新渲染所有线路（会自动应用电压等级筛选）
    await this.renderAllLines(linesData, filters)

    this.emit('filtersApplied', { filters, summary: this.getRenderSummary() })
  }

  /**
   * 应用电压等级筛选条件
   * @param {Array} voltageFilters - 电压等级筛选条件
   * @param {Object} linesData - 线路数据
   */
  async applyVoltageFilters(voltageFilters, linesData) {
    this.activeVoltageFilters = voltageFilters
    console.log('应用电压等级筛选条件:', voltageFilters)

    // 重新渲染所有线路（会自动应用状态筛选）
    await this.renderAllLines(linesData, this.activeFilters)

    this.emit('voltageFiltersApplied', { voltageFilters, summary: this.getRenderSummary() })
  }

  /**
   * 应用线路级别筛选条件
   * @param {Array} levelFilters - 线路级别筛选条件
   * @param {Object} linesData - 线路数据
   */
  async applyLevelFilters(levelFilters, linesData) {
    this.activeLevelFilters = levelFilters
    console.log('应用线路级别筛选条件:', levelFilters)

    // 重新渲染所有线路（会自动应用状态和电压等级筛选）
    await this.renderAllLines(linesData, this.activeFilters)

    this.emit('levelFiltersApplied', { levelFilters, summary: this.getRenderSummary() })
  }

  /**
   * 同时应用状态、电压等级和线路级别筛选条件
   * @param {Array} statusFilters - 状态筛选条件
   * @param {Array} voltageFilters - 电压等级筛选条件
   * @param {Array} levelFilters - 线路级别筛选条件
   * @param {Object} linesData - 线路数据
   */
  async applyCombinedFilters(statusFilters, voltageFilters, levelFilters, linesData) {
    this.activeFilters = statusFilters
    this.activeVoltageFilters = voltageFilters
    this.activeLevelFilters = levelFilters || this.activeLevelFilters

    console.log('应用组合筛选条件:', {
      status: statusFilters,
      voltage: voltageFilters,
      level: this.activeLevelFilters
    })

    // 重新渲染所有线路
    await this.renderAllLines(linesData, statusFilters)

    this.emit('combinedFiltersApplied', {
      statusFilters,
      voltageFilters,
      levelFilters: this.activeLevelFilters,
      summary: this.getRenderSummary()
    })
  }

  /**
   * 清除所有线路
   */
  clearAllLines() {
    // 从地图中移除所有线路对象
    this.renderedLines.forEach((group) => {
      group.objects.forEach(obj => {
        this.map.remove(obj)
      })
    })
    
    // 清空存储
    this.renderedLines.clear()
    this.lineObjects.clear()
    
    console.log('所有线路已清除')
  }

  /**
   * 清除指定电压等级的线路
   * @param {string} voltageLevel - 电压等级
   */
  clearLinesByVoltage(voltageLevel) {
    const group = this.renderedLines.get(voltageLevel)
    if (group) {
      group.objects.forEach(obj => {
        this.map.remove(obj)
      })
      this.renderedLines.delete(voltageLevel)
      console.log(`${voltageLevel} 线路已清除`)
    }
  }

  /**
   * 获取渲染摘要
   * @returns {Object} 渲染摘要信息
   */
  getRenderSummary() {
    const summary = {
      isRendering: this.isRendering,
      totalObjects: 0,
      voltageBreakdown: {},
      activeFilters: [...this.activeFilters],
      activeVoltageFilters: [...this.activeVoltageFilters],
      activeLevelFilters: [...this.activeLevelFilters],
      renderError: this.renderError
    }
    
    this.renderedLines.forEach((group, voltageLevel) => {
      summary.voltageBreakdown[voltageLevel] = {
        lineCount: group.lines.length,
        objectCount: group.objects.length,
        lastRendered: group.lastRendered
      }
      summary.totalObjects += group.objects.length
    })
    
    return summary
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器执行错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    this.clearAllLines()
    this.eventListeners.clear()
    this.map = null
    this.AMap = null
    console.log('PowerLineRenderer 已销毁')
  }
}

export default PowerLineRenderer
