/**
 * 地名标签管理器
 * 负责云南省各地区地名标签的创建、显示、隐藏和样式管理
 */

import { extractPlaceNames, formatPlaceName, getPlaceNameStyle, isValidCoordinates, PLACE_NAME_CONFIG } from '../utils/geoJsonParser.js'

export default class PlaceNameManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    this.placeNames = []
    this.markers = []
    this.visible = PLACE_NAME_CONFIG.defaultVisible
    this.currentZoom = 7
    
    // 初始化地名数据
    this.initPlaceNames()
    
    // 监听地图缩放事件
    this.bindEvents()
    
    console.log('地名标签管理器初始化完成')
  }

  /**
   * 初始化地名数据
   */
  initPlaceNames() {
    try {
      this.placeNames = extractPlaceNames()
      console.log(`成功加载 ${this.placeNames.length} 个地名`)
    } catch (error) {
      console.error('初始化地名数据失败:', error)
      this.placeNames = []
    }
  }

  /**
   * 绑定地图事件
   */
  bindEvents() {
    if (this.map) {
      // 监听缩放变化
      this.map.on('zoomend', () => {
        this.currentZoom = this.map.getZoom()
        this.updateVisibility()
      })
    }
  }

  /**
   * 创建地名标签
   */
  createPlaceNameMarkers() {
    // 清除现有标签
    this.clearMarkers()

    this.placeNames.forEach(place => {
      if (!isValidCoordinates(place.center)) {
        console.warn(`地区 ${place.name} 坐标无效:`, place.center)
        return
      }

      try {
        // 创建标签内容
        const labelContent = this.createLabelContent(place)
        
        // 创建标记
        const marker = new this.AMap.Marker({
          position: new this.AMap.LngLat(place.center[0], place.center[1]),
          content: labelContent,
          offset: new this.AMap.Pixel(...PLACE_NAME_CONFIG.offset),
          anchor: 'center',
          zIndex: getPlaceNameStyle(place.level).zIndex
        })

        // 存储地名信息到标记
        marker.placeData = place
        
        this.markers.push(marker)
        
      } catch (error) {
        console.error(`创建地名标签失败: ${place.name}`, error)
      }
    })

    console.log(`创建了 ${this.markers.length} 个地名标签`)
  }

  /**
   * 创建标签HTML内容
   * @param {Object} place - 地名信息
   * @returns {string} HTML内容
   */
  createLabelContent(place) {
    const style = getPlaceNameStyle(place.level)
    const displayName = formatPlaceName(place.name)
    
    return `
      <div class="place-name-label" style="
        font-size: ${style.fontSize};
        font-weight: ${style.fontWeight};
        color: ${style.color};
        background-color: ${style.backgroundColor};
        padding: ${style.padding};
        border-radius: ${style.borderRadius};
        white-space: nowrap;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        user-select: none;
        pointer-events: none;
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        transition: all ${PLACE_NAME_CONFIG.animation.duration}ms ${PLACE_NAME_CONFIG.animation.easing};
      ">
        ${displayName}
      </div>
    `
  }

  /**
   * 显示地名标签
   */
  show() {
    if (this.visible) return

    this.visible = true
    
    // 如果还没有创建标签，先创建
    if (this.markers.length === 0) {
      this.createPlaceNameMarkers()
    }

    // 根据当前缩放级别决定是否显示
    this.updateVisibility()
    
    console.log('地名标签已显示')
  }

  /**
   * 隐藏地名标签
   */
  hide() {
    if (!this.visible) return

    this.visible = false
    
    // 从地图上移除所有标签
    this.markers.forEach(marker => {
      if (marker.getMap()) {
        this.map.remove(marker)
      }
    })
    
    console.log('地名标签已隐藏')
  }

  /**
   * 根据缩放级别更新可见性
   */
  updateVisibility() {
    if (!this.visible) return

    const shouldShow = this.currentZoom >= PLACE_NAME_CONFIG.minZoom && 
                      this.currentZoom <= PLACE_NAME_CONFIG.maxZoom

    this.markers.forEach(marker => {
      if (shouldShow && !marker.getMap()) {
        // 添加到地图
        this.map.add(marker)
      } else if (!shouldShow && marker.getMap()) {
        // 从地图移除
        this.map.remove(marker)
      }
    })
  }

  /**
   * 清除所有标签
   */
  clearMarkers() {
    this.markers.forEach(marker => {
      if (marker.getMap()) {
        this.map.remove(marker)
      }
    })
    this.markers = []
  }

  /**
   * 切换显示状态
   */
  toggle() {
    if (this.visible) {
      this.hide()
    } else {
      this.show()
    }
  }

  /**
   * 获取当前显示状态
   * @returns {boolean} 是否显示
   */
  isVisible() {
    return this.visible
  }

  /**
   * 更新配置
   * @param {Object} config - 新配置
   */
  updateConfig(config) {
    Object.assign(PLACE_NAME_CONFIG, config)
    
    // 如果标签正在显示，重新创建以应用新配置
    if (this.visible) {
      this.createPlaceNameMarkers()
      this.updateVisibility()
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.clearMarkers()
    
    // 移除事件监听
    if (this.map) {
      this.map.off('zoomend')
    }
    
    this.map = null
    this.AMap = null
    this.placeNames = []
    
    console.log('地名标签管理器已销毁')
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      totalPlaces: this.placeNames.length,
      totalMarkers: this.markers.length,
      visibleMarkers: this.markers.filter(marker => marker.getMap()).length,
      isVisible: this.visible,
      currentZoom: this.currentZoom
    }
  }
}
