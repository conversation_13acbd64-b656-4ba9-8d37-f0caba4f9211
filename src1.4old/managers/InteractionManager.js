/**
 * 交互管理器
 * 处理地图上所有元素的鼠标交互事件
 * 管理悬停提示框和详细信息面板的显示
 */

export class InteractionManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    
    // 交互状态
    this.currentHoverTarget = null
    this.currentDetailTarget = null
    
    // 悬停提示框状态
    this.hoverTooltipVisible = false
    this.hoverTooltipData = null
    this.hoverTooltipPosition = null
    
    // 详细信息面板状态
    this.detailPanelVisible = false
    this.detailPanelData = null
    this.detailPanelType = null
    
    // 事件监听器
    this.eventListeners = new Map()
    
    // 线路对象引用（由PowerLineRenderer提供）
    this.lineObjects = new Map()
    
    console.log('InteractionManager 初始化完成')
  }

  /**
   * 初始化交互事件
   * @param {Object} powerLineRenderer - 线路渲染器实例
   * @param {Object} massMarkerManager - 海量点管理器实例
   */
  initInteractions(powerLineRenderer, massMarkerManager) {
    // 设置线路交互事件
    this.setupLineInteractions(powerLineRenderer)
    
    // 设置海量点交互事件
    this.setupMassMarkerInteractions(massMarkerManager)
    
    // 设置地图点击事件
    this.setupMapClickEvents()
    
    console.log('交互事件初始化完成')
  }

  /**
   * 设置线路交互事件
   * @param {Object} powerLineRenderer - 线路渲染器实例
   */
  setupLineInteractions(powerLineRenderer) {
    // 监听线路渲染完成事件
    powerLineRenderer.on('renderComplete', (summary) => {
      this.updateLineObjects(powerLineRenderer.lineObjects)
    })
    
    // 为每个线路对象添加事件监听
    this.setupLineHoverEvents()
    this.setupLineClickEvents()
  }

  /**
   * 更新线路对象引用
   * @param {Map} lineObjects - 线路对象Map
   */
  updateLineObjects(lineObjects) {
    this.lineObjects = lineObjects
    
    // 为新的线路对象添加事件监听
    this.lineObjects.forEach((objects, lineId) => {
      objects.forEach(lineObj => {
        this.addLineEventListeners(lineObj)
      })
    })
  }

  /**
   * 为线路对象添加事件监听
   * @param {Object} lineObj - 线路对象
   */
  addLineEventListeners(lineObj) {
    // 鼠标悬停事件
    lineObj.on('mouseover', (e) => {
      this.handleLineHover(e)
    })
    
    // 鼠标离开事件
    lineObj.on('mouseout', (e) => {
      this.handleLineMouseOut(e)
    })
    
    // 点击事件
    lineObj.on('click', (e) => {
      this.handleLineClick(e)
    })
  }

  /**
   * 设置线路悬停事件
   */
  setupLineHoverEvents() {
    // 这个方法在updateLineObjects中被调用
    console.log('线路悬停事件设置完成')
  }

  /**
   * 设置线路点击事件
   */
  setupLineClickEvents() {
    // 这个方法在updateLineObjects中被调用
    console.log('线路点击事件设置完成')
  }

  /**
   * 设置海量点交互事件
   * @param {Object} massMarkerManager - 海量点管理器实例
   */
  setupMassMarkerInteractions(massMarkerManager) {
    // 监听电塔点击事件
    massMarkerManager.on('towerClick', (data) => {
      this.handleTowerClick(data)
    })
    
    // 监听变电站点击事件
    massMarkerManager.on('substationClick', (data) => {
      this.handleSubstationClick(data)
    })
    
    // 监听发电站点击事件
    massMarkerManager.on('powerPlantClick', (data) => {
      this.handlePowerPlantClick(data)
    })
    
    console.log('海量点交互事件设置完成')
  }

  /**
   * 设置地图点击事件
   */
  setupMapClickEvents() {
    this.map.on('click', (e) => {
      // 检查是否点击在空白区域
      if (!e.target || e.target === this.map) {
        this.hideDetailPanel()
      }
    })
    
    console.log('地图点击事件设置完成')
  }

  /**
   * 处理线路悬停事件
   * @param {Object} e - 事件对象
   */
  handleLineHover(e) {
    const extData = e.target.getExtData()

    if (extData && extData.type === 'powerline') {
      this.showHoverTooltip({
        type: 'powerline',
        name: extData.lineName,
        voltageLevel: extData.voltageLevel,
        status: extData.status,
        length: extData.length,
        lineLevel: extData.lineLevel // 添加线路级别
      }, e.lnglat)

      this.currentHoverTarget = e.target
    }
  }

  /**
   * 处理线路鼠标离开事件
   * @param {Object} e - 事件对象
   */
  handleLineMouseOut(e) {
    this.hideHoverTooltip()
    this.currentHoverTarget = null
  }

  /**
   * 处理线路点击事件
   * @param {Object} e - 事件对象
   */
  handleLineClick(e) {
    const extData = e.target.getExtData()

    if (extData && extData.type === 'powerline') {
      this.showDetailPanel({
        type: 'powerline',
        lineId: extData.lineId,
        lineName: extData.lineName,
        voltageLevel: extData.voltageLevel,
        status: extData.status,
        length: extData.length,
        lineLevel: extData.lineLevel, // 添加线路级别
        constructionProgress: extData.constructionProgress, // 添加建设进度
        segments: extData.segments, // 添加分段数据
        description: extData.description, // 添加描述
        detailInfo: extData.detailInfo
      }, 'powerline')

      this.currentDetailTarget = e.target
    }

    // 阻止事件冒泡到地图
    e.stopPropagation()
  }

  /**
   * 处理电塔点击事件
   * @param {Object} data - 事件数据
   */
  handleTowerClick(data) {
    this.showDetailPanel({
      type: 'tower',
      towerId: data.data.towerId,
      powerlineName: data.data.powerlineName,
      voltageLevel: data.data.voltageLevel,
      detailInfo: data.data.detailInfo
    }, 'tower')
  }

  /**
   * 处理变电站点击事件
   * @param {Object} data - 事件数据
   */
  handleSubstationClick(data) {
    this.showDetailPanel({
      type: 'substation',
      stationId: data.data.stationId,
      stationName: data.data.stationName,
      voltageLevel: data.data.voltageLevel,
      capacity: data.data.capacity,
      detailInfo: data.data.detailInfo
    }, 'substation')
  }

  /**
   * 处理发电站点击事件
   * @param {Object} data - 事件数据
   */
  handlePowerPlantClick(data) {
    this.showDetailPanel({
      type: 'powerPlant',
      plantId: data.data.plantId,
      plantName: data.data.plantName,
      capacity: data.data.capacity,
      generationType: data.data.generationType,
      detailInfo: data.data.detailInfo
    }, 'powerPlant')
  }

  /**
   * 显示悬停提示框
   * @param {Object} data - 提示数据
   * @param {Object} position - 位置信息
   */
  showHoverTooltip(data, position) {
    this.hoverTooltipData = data
    this.hoverTooltipPosition = position
    this.hoverTooltipVisible = true
    
    this.emit('showHoverTooltip', {
      data: data,
      position: position,
      visible: true
    })
  }

  /**
   * 隐藏悬停提示框
   */
  hideHoverTooltip() {
    this.hoverTooltipVisible = false
    this.hoverTooltipData = null
    this.hoverTooltipPosition = null
    
    this.emit('hideHoverTooltip', {
      visible: false
    })
  }

  /**
   * 显示详细信息面板
   * @param {Object} data - 详细数据
   * @param {string} type - 数据类型
   */
  showDetailPanel(data, type) {
    // 先隐藏悬停提示框
    this.hideHoverTooltip()
    
    this.detailPanelData = data
    this.detailPanelType = type
    this.detailPanelVisible = true
    
    this.emit('showDetailPanel', {
      data: data,
      type: type,
      visible: true
    })
  }

  /**
   * 隐藏详细信息面板
   */
  hideDetailPanel() {
    this.detailPanelVisible = false
    this.detailPanelData = null
    this.detailPanelType = null
    this.currentDetailTarget = null
    
    this.emit('hideDetailPanel', {
      visible: false
    })
  }

  /**
   * 获取当前悬停提示框状态
   * @returns {Object} 悬停提示框状态
   */
  getHoverTooltipState() {
    return {
      visible: this.hoverTooltipVisible,
      data: this.hoverTooltipData,
      position: this.hoverTooltipPosition
    }
  }

  /**
   * 获取当前详细信息面板状态
   * @returns {Object} 详细信息面板状态
   */
  getDetailPanelState() {
    return {
      visible: this.detailPanelVisible,
      data: this.detailPanelData,
      type: this.detailPanelType
    }
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器执行错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners() {
    // 清理所有事件监听器
    this.eventListeners.clear()

    // 移除地图事件监听
    this.map.off('click')

    console.log('所有事件监听器已移除')
  }

  /**
   * 销毁管理器
   */
  destroy() {
    // 隐藏所有UI
    this.hideHoverTooltip()
    this.hideDetailPanel()

    // 移除所有事件监听器
    this.removeAllListeners()

    // 清理数据
    this.lineObjects.clear()
    this.map = null
    this.AMap = null

    console.log('InteractionManager 已销毁')
  }
}

export default InteractionManager
