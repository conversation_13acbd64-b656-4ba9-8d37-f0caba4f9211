<template>
  <div 
    class="hover-tooltip" 
    v-show="visible" 
    :style="tooltipStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="tooltip-content">
      <!-- 线路信息 -->
      <div v-if="data && data.type === 'powerline'" class="powerline-tooltip">
        <div class="tooltip-header">
          <el-icon class="header-icon"><Connection /></el-icon>
          <h4 class="tooltip-title">{{ data.name }}</h4>
        </div>
        <div class="tooltip-body">
          <div class="info-item">
            <span class="label">电压等级:</span>
            <span class="value voltage-badge" :class="getVoltageBadgeClass(data.voltageLevel)">
              {{ data.voltageLevel }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">线路级别:</span>
            <span class="value level-badge" :class="getLevelBadgeClass(data.lineLevel)">
              {{ getLevelName(data.lineLevel) }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">当前状态:</span>
            <span class="value status-badge" :class="getStatusBadgeClass(data.status)">
              {{ data.status }}
            </span>
          </div>
          <div class="info-item" v-if="data.length">
            <span class="label">线路长度:</span>
            <span class="value">{{ data.length }}km</span>
          </div>
        </div>
      </div>

      <!-- 电塔信息 -->
      <div v-else-if="data && data.type === 'tower'" class="tower-tooltip">
        <div class="tooltip-header">
          <el-icon class="header-icon"><Connection /></el-icon>
          <h4 class="tooltip-title">电塔 {{ data.towerId }}</h4>
        </div>
        <div class="tooltip-body">
          <div class="info-item">
            <span class="label">所属线路:</span>
            <span class="value">{{ data.powerlineName }}</span>
          </div>
          <div class="info-item">
            <span class="label">电压等级:</span>
            <span class="value voltage-badge" :class="getVoltageBadgeClass(data.voltageLevel)">
              {{ data.voltageLevel }}
            </span>
          </div>
        </div>
      </div>

      <!-- 变电站信息 -->
      <div v-else-if="data && data.type === 'substation'" class="substation-tooltip">
        <div class="tooltip-header">
          <el-icon class="header-icon"><OfficeBuilding /></el-icon>
          <h4 class="tooltip-title">{{ data.stationName }}</h4>
        </div>
        <div class="tooltip-body">
          <div class="info-item">
            <span class="label">电压等级:</span>
            <span class="value voltage-badge" :class="getVoltageBadgeClass(data.voltageLevel)">
              {{ data.voltageLevel }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">变电容量:</span>
            <span class="value">{{ data.capacity }}</span>
          </div>
        </div>
      </div>

      <!-- 发电站信息 -->
      <div v-else-if="data && data.type === 'powerPlant'" class="powerplant-tooltip">
        <div class="tooltip-header">
          <el-icon class="header-icon"><OfficeBuilding /></el-icon>
          <h4 class="tooltip-title">{{ data.plantName }}</h4>
        </div>
        <div class="tooltip-body">
          <div class="info-item">
            <span class="label">发电类型:</span>
            <span class="value">{{ getGenerationTypeName(data.generationType) }}</span>
          </div>
          <div class="info-item">
            <span class="label">装机容量:</span>
            <span class="value">{{ data.capacity }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 提示箭头 -->
    <div class="tooltip-arrow"></div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: null
  },
  position: {
    type: Object,
    default: null
  }
})

// 响应式数据
const mouseInTooltip = ref(false)

// 计算提示框样式
const tooltipStyle = computed(() => {
  if (!props.position || !props.visible) {
    return {
      display: 'none'
    }
  }

  // 将地理坐标转换为屏幕坐标
  // 这里需要根据实际的地图实例来转换坐标
  // 暂时使用固定偏移
  return {
    position: 'fixed',
    left: '50%',
    top: '50%',
    transform: 'translate(-50%, -100%)',
    zIndex: 9999,
    pointerEvents: 'auto'
  }
})

// 获取电压等级徽章样式类
const getVoltageBadgeClass = (voltageLevel) => {
  const classMap = {
    '500KV': 'voltage-500kv',
    '220KV': 'voltage-220kv',
    '110KV': 'voltage-110kv',
    '35KV': 'voltage-35kv',
    '10KV': 'voltage-10kv'
  }
  return classMap[voltageLevel] || 'voltage-default'
}

// 获取状态徽章样式类
const getStatusBadgeClass = (status) => {
  const classMap = {
    '已完成': 'status-completed',
    '建设中': 'status-construction',
    '规划中': 'status-planning',
    '检修中': 'status-maintenance',
    '停运': 'status-shutdown'
  }
  return classMap[status] || 'status-default'
}

// 获取发电类型名称
const getGenerationTypeName = (type) => {
  const nameMap = {
    'thermal': '火力发电',
    'hydro': '水力发电',
    'wind': '风力发电',
    'solar': '太阳能发电'
  }
  return nameMap[type] || type
}

// 获取线路级别徽章样式类
const getLevelBadgeClass = (level) => {
  // 如果级别为空、未定义或null，返回其他样式
  if (!level || level.trim() === '') {
    return 'level-other'
  }

  const classMap = {
    '国际': 'level-international',
    '国重': 'level-national',
    '省重': 'level-provincial',
    '战略': 'level-strategic',
    '应急': 'level-emergency',
    '民生': 'level-livelihood',
    '其他': 'level-other'
  }
  return classMap[level] || 'level-other'
}

// 获取线路级别名称
const getLevelName = (level) => {
  // 如果级别为空、未定义或null，返回"其他项目"
  if (!level || level.trim() === '') {
    return '其他项目'
  }

  const nameMap = {
    '国际': '国际合作项目',
    '国重': '国家重点项目',
    '省重': '省重点项目',
    '战略': '战略项目',
    '应急': '应急保障',
    '民生': '民生工程',
    '其他': '其他项目'
  }
  return nameMap[level] || '其他项目'
}

// 处理鼠标进入提示框
const handleMouseEnter = () => {
  mouseInTooltip.value = true
}

// 处理鼠标离开提示框
const handleMouseLeave = () => {
  mouseInTooltip.value = false
}

// 监听鼠标状态变化
watch(mouseInTooltip, (inTooltip) => {
  if (!inTooltip && props.visible) {
    // 延迟隐藏，给用户时间移动鼠标
    setTimeout(() => {
      if (!mouseInTooltip.value) {
        // 触发隐藏事件
        // 这里应该通过事件通知父组件隐藏提示框
      }
    }, 100)
  }
})
</script>

<style scoped>
.hover-tooltip {
  position: fixed;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
  min-width: 200px;
  max-width: 300px;
  font-size: 12px;
  line-height: 1.4;
  z-index: 9999;
  pointer-events: auto;
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -100%) scale(1);
  }
}

.tooltip-content {
  padding: 12px;
}

.tooltip-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-icon {
  font-size: 16px;
  color: #1890ff;
  margin-right: 6px;
}

.tooltip-title {
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.tooltip-body {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  color: #666;
  font-size: 11px;
  margin-right: 8px;
}

.value {
  color: #333;
  font-size: 11px;
  font-weight: 500;
}

/* 电压等级徽章样式 */
.voltage-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  color: white;
}

.voltage-500kv {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.voltage-220kv {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.voltage-110kv {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.voltage-35kv {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.voltage-10kv {
  background: linear-gradient(135deg, #13c2c2, #36cfc9);
}

.voltage-default {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
}

/* 状态徽章样式 */
.status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  color: white;
}

.status-completed {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.status-construction {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.status-planning {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.status-maintenance {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.status-shutdown {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.status-default {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
}

/* 线路级别徽章样式 */
.level-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  color: white;
}

.level-international {
  background: linear-gradient(135deg, #ffd666, #ffc53d);
  color: #333;
}

.level-national {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.level-provincial {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.level-strategic {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.level-emergency {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.level-livelihood {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.level-other {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
}

.level-default {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
}

/* 提示箭头 */
.tooltip-arrow {
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(255, 255, 255, 0.95);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hover-tooltip {
    min-width: 180px;
    max-width: 250px;
    font-size: 11px;
  }
  
  .tooltip-content {
    padding: 10px;
  }
  
  .tooltip-title {
    font-size: 12px;
  }
  
  .label, .value {
    font-size: 10px;
  }
}
</style>
