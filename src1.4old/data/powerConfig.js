/**
 * 电力系统配置文件
 * 包含样式配置、状态配置等
 * 符合新项目设计要求
 */

// 线路样式配置
export const LINE_STYLES = {
  voltageStyles: {
    '500KV': {
      strokeWeight: 8,
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '500千伏',
      priority: 1
    },
    '220KV': {
      strokeWeight: 6,
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '220千伏',
      priority: 2
    },
    '110KV': {
      strokeWeight: 4,
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '110千伏',
      priority: 3
    },
    '35KV': {
      strokeWeight: 3,
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '35千伏',
      priority: 4
    },
    '10KV': {
      strokeWeight: 2,
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '10千伏',
      priority: 5
    }
  },
  statusStyles: {
    '已完成': {
      strokeStyle: 'solid',
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '已完成',
      animation: false
    },
    '建设中': {
      strokeStyle: 'dashed',
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '建设中',
      animation: true
    },
    '规划中': {
      strokeStyle: 'dotted',
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '规划中',
      animation: false
    },
    '检修中': {
      strokeStyle: 'dashed',
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '检修中',
      animation: true
    },
    '停运': {
      strokeStyle: 'solid',
      color: '#1890ff', // 蓝色 - 统一颜色
      name: '停运',
      animation: false
    }
  }
}

// 设施类型配置
export const FACILITY_STYLES = {
  substationTypes: {
    'substation': {
      name: '变电站',
      color: '#1890ff',
      icon: 'substation',
      size: 'large'
    },
    'converter_station': {
      name: '换流站',
      color: '#1890ff',
      icon: 'converter',
      size: 'large'
    }
  },
  powerPlantTypes: {
    'thermal': {
      name: '火力发电厂',
      color: '#1890ff',
      icon: 'thermal',
      size: 'large'
    },
    'hydro': {
      name: '水力发电站',
      color: '#1890ff',
      icon: 'hydro',
      size: 'large'
    },
    'wind': {
      name: '风力发电场',
      color: '#1890ff',
      icon: 'wind',
      size: 'medium'
    },
    'solar': {
      name: '太阳能发电站',
      color: '#1890ff',
      icon: 'solar',
      size: 'medium'
    }
  },
  towerTypes: {
    'tower': {
      name: '电塔',
      color: '#1890ff',
      icon: 'tower',
      size: 'small'
    }
  }
}

// 电压等级配置
export const VOLTAGE_CONFIG = {
  '500KV': {
    name: '500千伏',
    color: '#1890ff', // 蓝色 - 统一颜色
    strokeWeight: 8,
    priority: 1,
    displayOrder: 1
  },
  '220KV': {
    name: '220千伏',
    color: '#1890ff', // 蓝色 - 统一颜色
    strokeWeight: 6,
    priority: 2,
    displayOrder: 2
  },
  '110KV': {
    name: '110千伏',
    color: '#1890ff', // 蓝色 - 统一颜色
    strokeWeight: 4,
    priority: 3,
    displayOrder: 3
  },
  '35KV': {
    name: '35千伏',
    color: '#1890ff', // 蓝色 - 统一颜色
    strokeWeight: 3,
    priority: 4,
    displayOrder: 4
  },
  '10KV': {
    name: '10千伏',
    color: '#1890ff', // 蓝色 - 统一颜色
    strokeWeight: 2,
    priority: 5,
    displayOrder: 5
  }
}

// 项目重要性等级配置
export const PROJECT_IMPORTANCE_CONFIG = {
  '民生': {
    name: '民生工程',
    color: '#52c41a',
    priority: 'medium',
    badge: '民生',
    displayOrder: 5
  },
  '应急': {
    name: '应急保障',
    color: '#1890ff',
    priority: 'high',
    badge: '应急',
    displayOrder: 4
  },
  '战略': {
    name: '战略项目',
    color: '#722ed1',
    priority: 'high',
    badge: '战略',
    displayOrder: 3
  },
  '省重': {
    name: '省重点项目',
    color: '#fa8c16',
    priority: 'high',
    badge: '省重',
    displayOrder: 2
  },
  '国重': {
    name: '国家重点项目',
    color: '#ff4d4f',
    priority: 'critical',
    badge: '国重',
    displayOrder: 1
  },
  '国际': {
    name: '国际合作项目',
    color: '#ffd666',
    priority: 'critical',
    badge: '国际',
    displayOrder: 0
  },
  '其他': {
    name: '其他项目',
    color: '#8c8c8c',
    priority: 'low',
    badge: '其他',
    displayOrder: 6
  }
}

// 线路状态配置
export const LINE_STATUS_CONFIG = {
  '已完成': {
    name: '已完成',
    color: '#1890ff', // 蓝色 - 统一颜色
    style: 'solid',
    animation: false,
    displayOrder: 1
  },
  '建设中': {
    name: '建设中',
    color: '#1890ff', // 蓝色 - 统一颜色
    style: 'dashed',
    animation: true,
    displayOrder: 2
  },
  '规划中': {
    name: '规划中',
    color: '#1890ff', // 蓝色 - 统一颜色
    style: 'dotted',
    animation: false,
    displayOrder: 3
  },
  '检修中': {
    name: '检修中',
    color: '#1890ff', // 蓝色 - 统一颜色
    style: 'dashed',
    animation: true,
    displayOrder: 4
  },
  '停运': {
    name: '停运',
    color: '#1890ff', // 蓝色 - 统一颜色
    style: 'solid',
    animation: false,
    displayOrder: 5
  }
}

// 地图配置
export const MAP_CONFIG = {
  // 云南省边界
  bounds: {
    north: 29.15,
    south: 21.13,
    east: 106.2,
    west: 97.53
  },
  // 默认中心点（昆明）
  center: [102.8329, 24.8801],
  // 默认缩放级别
  defaultZoom: 7,
  // 最小/最大缩放级别
  minZoom: 5,
  maxZoom: 15,
  // 3D模型加载的最小缩放级别
  model3DMinZoom: 12
}

// 缩放级别阈值配置
export const ZOOM_THRESHOLDS = {
  // 开始显示3D模型的缩放级别（11以上显示模型，11以下显示图标）
  SHOW_3D_MODELS: 11,
  // 显示详细模型和标签的缩放级别
  SHOW_DETAILED_MODELS: 15,
  // 隐藏图标的缩放级别（当显示3D模型时）
  HIDE_ICONS: 12,
  // 显示重点项目标签的缩放级别
  SHOW_KEY_PROJECT_LABELS: 8,
  // 显示电塔的最小缩放级别（优化密度显示）
  SHOW_TOWERS: 10,
  // 显示所有电塔的缩放级别
  SHOW_ALL_TOWERS: 12
}

// 性能配置
export const PERFORMANCE_CONFIG = {
  // 海量点渲染阈值
  massMarkerThreshold: 100,
  // 3D模型加载距离（米）
  model3DLoadDistance: 50000,
  // 数据分页大小
  dataPageSize: 50,
  // 动画帧率
  animationFPS: 30,
  // 3D模型性能配置
  MAX_VISIBLE_MODELS: 50, // 最大可见模型数量
  MAX_POOL_SIZE: 20, // 对象池最大大小
  LOAD_BATCH_SIZE: 10, // 批量加载大小
  DEBOUNCE_DELAY: 300 // 防抖延迟时间（毫秒）
}

// 导出所有配置
export default {
  LINE_STYLES,
  FACILITY_STYLES,
  VOLTAGE_CONFIG,
  PROJECT_IMPORTANCE_CONFIG,
  LINE_STATUS_CONFIG,
  MAP_CONFIG,
  ZOOM_THRESHOLDS,
  PERFORMANCE_CONFIG
}
