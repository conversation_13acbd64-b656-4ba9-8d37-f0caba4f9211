/**
 * 数据模块统一导出文件
 * 提供便捷的数据访问接口
 */

// 导入所有数据模块
import { powerLinesData } from './powerLines.js'
import { powerFacilitiesData } from './powerFacilities.js'
import {
  LINE_STYLES,
  FACILITY_STYLES,
  VOLTAGE_CONFIG,
  PROJECT_IMPORTANCE_CONFIG,
  LINE_STATUS_CONFIG,
  MAP_CONFIG,
  ZOOM_THRESHOLDS,
  PERFORMANCE_CONFIG
} from './powerConfig.js'

// 统一导出所有数据
export {
  // 线路数据
  powerLinesData,

  // 设施数据
  powerFacilitiesData,

  // 配置数据
  LINE_STYLES,
  FACILITY_STYLES,
  VOLTAGE_CONFIG,
  PROJECT_IMPORTANCE_CONFIG,
  LINE_STATUS_CONFIG,
  MAP_CONFIG,
  ZOOM_THRESHOLDS,
  PERFORMANCE_CONFIG
}

// 便捷的数据访问函数
export const getPowerLinesByVoltage = (voltage) => {
  return powerLinesData[voltage] || []
}

export const getPowerLinesByStatus = (status) => {
  const result = []
  Object.values(powerLinesData).forEach(voltageLines => {
    result.push(...voltageLines.filter(line => line.status === status))
  })
  return result
}

export const getSubstationsByVoltage = (voltage) => {
  return powerFacilitiesData.substations.filter(sub => sub.voltageLevel === voltage)
}

export const getPowerPlantsByType = (type) => {
  return powerFacilitiesData.powerPlants.filter(plant => plant.type === type)
}

export const getAllVoltageTypes = () => {
  return Object.keys(VOLTAGE_CONFIG).sort((a, b) => 
    VOLTAGE_CONFIG[a].displayOrder - VOLTAGE_CONFIG[b].displayOrder
  )
}

export const getAllLineStatuses = () => {
  return Object.keys(LINE_STATUS_CONFIG).sort((a, b) =>
    LINE_STATUS_CONFIG[a].displayOrder - LINE_STATUS_CONFIG[b].displayOrder
  )
}

/**
 * 从电力线路数据中提取所有电塔位置
 * 用于3D模型加载
 * @returns {Array} 电塔位置数组
 */
export const extractTowerPositions = () => {
  const towers = []
  const positionMap = new Map() // 用于去重相同位置的电塔

  // 遍历所有电压等级的线路数据
  Object.entries(powerLinesData).forEach(([voltage, lines]) => {
    lines.forEach(line => {
      // 确保线路有电塔数据
      if (line.towers && Array.isArray(line.towers)) {
        line.towers.forEach(tower => {
          const positionKey = `${tower.position[0]}_${tower.position[1]}`

          if (!positionMap.has(positionKey)) {
            // 创建电塔数据对象
            const towerData = {
              id: tower.id,
              name: tower.detailInfo?.powerlineName || line.name,
              position: tower.position,
              voltage: line.voltageLevel,
              status: line.status,
              progress: line.detailInfo?.loadRate || 100, // 使用负载率作为进度
              type: tower.detailInfo?.towerType || '直线塔',
              height: tower.detailInfo?.height || 45,
              info: {
                voltage: line.voltageLevel,
                type: tower.detailInfo?.towerType || '直线塔',
                height: `${tower.detailInfo?.height || 45}m`,
                altitude: tower.detailInfo?.altitude || 0,
                foundationType: tower.detailInfo?.foundationType || '钢筋混凝土基础',
                maintenanceStatus: tower.detailInfo?.maintenanceStatus || '良好'
              },
              detailInfo: tower.detailInfo,
              lineData: line,
              relatedLines: [line] // 记录相关的线路
            }

            towers.push(towerData)
            positionMap.set(positionKey, towerData)
          } else {
            // 如果位置已存在，添加到相关线路中
            const existingTower = positionMap.get(positionKey)
            existingTower.relatedLines.push(line)

            // 更新电压等级为最高的
            const voltageOrder = { '500KV': 4, '220KV': 3, '110KV': 2, '35KV': 1, '10KV': 0 }
            if ((voltageOrder[line.voltageLevel] || 0) > (voltageOrder[existingTower.voltage] || 0)) {
              existingTower.voltage = line.voltageLevel
              existingTower.info.voltage = line.voltageLevel
            }
          }
        })
      }
    })
  })

  console.log(`从电力线路数据中提取了 ${towers.length} 个去重后的电塔位置`)
  return towers
}

/**
 * 获取视野范围内的电塔
 * @param {Object} bounds - 地图边界对象
 * @param {Array} allTowers - 所有电塔数据（可选，不传则自动提取）
 * @returns {Array} 视野范围内的电塔数组
 */
export const getVisibleTowers = (bounds, allTowers = null) => {
  if (!bounds) {
    console.warn('未提供地图边界信息')
    return []
  }

  // 如果没有提供电塔数据，则自动提取
  const towers = allTowers || extractTowerPositions()

  try {
    // 尝试不同的方式获取边界坐标
    let swLng, swLat, neLng, neLat

    if (bounds.getSouthWest && bounds.getNorthEast) {
      // 高德地图的Bounds对象
      const southwest = bounds.getSouthWest()
      const northeast = bounds.getNorthEast()
      swLng = southwest.lng || southwest.getLng()
      swLat = southwest.lat || southwest.getLat()
      neLng = northeast.lng || northeast.getLng()
      neLat = northeast.lat || northeast.getLat()
    } else if (bounds.southwest && bounds.northeast) {
      // 自定义边界对象
      const southwest = bounds.southwest
      const northeast = bounds.northeast
      swLng = southwest.lng || southwest.getLng()
      swLat = southwest.lat || southwest.getLat()
      neLng = northeast.lng || northeast.getLng()
      neLat = northeast.lat || northeast.getLat()
    } else if (bounds._southWest && bounds._northEast) {
      // 另一种可能的边界对象格式
      const southwest = bounds._southWest
      const northeast = bounds._northEast
      swLng = southwest.lng || southwest.getLng()
      swLat = southwest.lat || southwest.getLat()
      neLng = northeast.lng || northeast.getLng()
      neLat = northeast.lat || northeast.getLat()
    } else if (bounds.CLASS_NAME === 'AMap.ArrayBounds' && bounds.bounds && bounds.bounds.length >= 4) {
      // AMap.ArrayBounds 格式 - 从bounds数组中提取边界
      const boundsArray = bounds.bounds
      // bounds数组通常包含多个点，我们需要找到最小和最大的经纬度
      let minLng = Infinity, minLat = Infinity, maxLng = -Infinity, maxLat = -Infinity

      boundsArray.forEach(point => {
        let lng, lat
        if (point.lng !== undefined && point.lat !== undefined) {
          lng = point.lng
          lat = point.lat
        } else if (point.getLng && point.getLat) {
          lng = point.getLng()
          lat = point.getLat()
        } else if (Array.isArray(point) && point.length >= 2) {
          lng = point[0]
          lat = point[1]
        } else {
          return // 跳过无法解析的点
        }

        minLng = Math.min(minLng, lng)
        minLat = Math.min(minLat, lat)
        maxLng = Math.max(maxLng, lng)
        maxLat = Math.max(maxLat, lat)
      })

      if (minLng !== Infinity && minLat !== Infinity && maxLng !== -Infinity && maxLat !== -Infinity) {
        swLng = minLng
        swLat = minLat
        neLng = maxLng
        neLat = maxLat
      } else {
        console.warn('AMap.ArrayBounds 边界解析失败:', bounds)
        return []
      }
    } else if (bounds.contains && typeof bounds.contains === 'function') {
      // 尝试使用contains方法来判断边界（备用方案）
      console.log('使用contains方法进行边界检查')
      return towers.filter(tower => {
        try {
          const [lng, lat] = tower.position
          const point = { lng, lat }
          return bounds.contains(point)
        } catch (error) {
          return false
        }
      })
    } else {
      console.warn('无法识别的边界对象格式:', bounds)
      console.log('边界对象详情:', {
        className: bounds.CLASS_NAME,
        keys: Object.keys(bounds),
        bounds: bounds
      })
      return []
    }

    // 筛选视野范围内的电塔
    return towers.filter(tower => {
      const [lng, lat] = tower.position
      return lng >= swLng && lng <= neLng && lat >= swLat && lat <= neLat
    })
  } catch (error) {
    console.error('获取视野内电塔时出错:', error)
    return []
  }
}

/**
 * 检查点是否在边界范围内
 * @param {Array} position - 位置坐标 [lng, lat]
 * @param {Object} bounds - 边界对象
 * @returns {boolean} 是否在范围内
 */
export const isPointInBounds = (position, bounds) => {
  if (!position || !bounds) return false

  const [lng, lat] = position
  const southwest = bounds.southwest || bounds.getSouthWest()
  const northeast = bounds.northeast || bounds.getNorthEast()

  const swLng = southwest.lng || southwest.getLng()
  const swLat = southwest.lat || southwest.getLat()
  const neLng = northeast.lng || northeast.getLng()
  const neLat = northeast.lat || northeast.getLat()

  return lng >= swLng && lng <= neLng && lat >= swLat && lat <= neLat
}

// 数据统计函数
export const getDataStatistics = () => {
  const stats = {
    totalLines: 0,
    linesByVoltage: {},
    linesByStatus: {},
    totalSubstations: powerFacilitiesData.substations.length,
    totalPowerPlants: powerFacilitiesData.powerPlants.length,
    substationsByVoltage: {},
    powerPlantsByType: {}
  }

  // 统计线路数据
  Object.entries(powerLinesData).forEach(([voltage, lines]) => {
    stats.totalLines += lines.length
    stats.linesByVoltage[voltage] = lines.length
    
    lines.forEach(line => {
      stats.linesByStatus[line.status] = (stats.linesByStatus[line.status] || 0) + 1
    })
  })

  // 统计变电站数据
  powerFacilitiesData.substations.forEach(sub => {
    stats.substationsByVoltage[sub.voltageLevel] = 
      (stats.substationsByVoltage[sub.voltageLevel] || 0) + 1
  })

  // 统计发电站数据
  powerFacilitiesData.powerPlants.forEach(plant => {
    stats.powerPlantsByType[plant.type] = 
      (stats.powerPlantsByType[plant.type] || 0) + 1
  })

  return stats
}

// 默认导出
export default {
  powerLinesData,
  powerFacilitiesData,
  LINE_STYLES,
  FACILITY_STYLES,
  VOLTAGE_CONFIG,
  PROJECT_IMPORTANCE_CONFIG,
  LINE_STATUS_CONFIG,
  MAP_CONFIG,
  ZOOM_THRESHOLDS,
  PERFORMANCE_CONFIG,

  // 便捷函数
  getPowerLinesByVoltage,
  getPowerLinesByStatus,
  getSubstationsByVoltage,
  getPowerPlantsByType,
  getAllVoltageTypes,
  getAllLineStatuses,
  getDataStatistics,

  // 3D模型相关函数
  extractTowerPositions,
  getVisibleTowers,
  isPointInBounds
}
