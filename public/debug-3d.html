<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D模型调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-item {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>3D模型调试工具</h1>
    
    <div class="debug-panel">
        <h3>系统状态检查</h3>
        <div id="system-status"></div>
        <button onclick="checkSystemStatus()">刷新状态</button>
        <button onclick="testGltfLoad()">测试GLTF加载</button>
        <button onclick="openMainApp()">打开主应用</button>
    </div>
    
    <div class="debug-panel">
        <h3>调试日志</h3>
        <div id="debug-log" class="log-area"></div>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <script>
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            
            const logArea = document.getElementById('debug-log');
            const div = document.createElement('div');
            div.textContent = logEntry;
            div.className = type;
            logArea.appendChild(div);
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(logEntry);
        }
        
        function clearLog() {
            debugLog = [];
            document.getElementById('debug-log').innerHTML = '';
        }
        
        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            let html = '';
            
            // 检查基础环境
            html += `<div class="status-item info">浏览器: ${navigator.userAgent}</div>`;
            html += `<div class="status-item info">当前URL: ${window.location.href}</div>`;
            
            // 检查文件访问
            fetch('/assets/model/dt.gltf')
                .then(response => {
                    if (response.ok) {
                        html += `<div class="status-item success">✓ GLTF模型文件可访问</div>`;
                        log('GLTF模型文件访问成功', 'success');
                    } else {
                        html += `<div class="status-item error">✗ GLTF模型文件访问失败 (${response.status})</div>`;
                        log(`GLTF模型文件访问失败: ${response.status}`, 'error');
                    }
                    statusDiv.innerHTML = html;
                })
                .catch(error => {
                    html += `<div class="status-item error">✗ GLTF模型文件访问错误: ${error.message}</div>`;
                    log(`GLTF模型文件访问错误: ${error.message}`, 'error');
                    statusDiv.innerHTML = html;
                });
            
            fetch('/assets/model/dt.bin')
                .then(response => {
                    if (response.ok) {
                        log('GLTF二进制文件访问成功', 'success');
                    } else {
                        log(`GLTF二进制文件访问失败: ${response.status}`, 'error');
                    }
                })
                .catch(error => {
                    log(`GLTF二进制文件访问错误: ${error.message}`, 'error');
                });
            
            statusDiv.innerHTML = html;
        }
        
        function testGltfLoad() {
            log('开始测试GLTF加载...', 'info');
            
            // 检查高德地图API
            if (typeof AMap === 'undefined') {
                log('高德地图API未加载，正在加载...', 'warning');
                loadAmapAPI();
                return;
            }
            
            log('高德地图API已加载', 'success');
            log(`AMap版本: ${AMap.version}`, 'info');
            
            // 检查GltfLoader
            if (!AMap.GltfLoader) {
                log('GltfLoader插件未加载', 'error');
                return;
            }
            
            log('GltfLoader插件已加载', 'success');
            
            // 尝试加载GLTF
            const gltfLoader = new AMap.GltfLoader();
            const modelUrl = '/assets/model/dt.gltf';
            
            log(`尝试加载模型: ${modelUrl}`, 'info');
            
            gltfLoader.load(modelUrl, function(gltfModel) {
                log('✓ GLTF模型加载成功!', 'success');
                log(`模型对象: ${gltfModel}`, 'info');
            }, function(error) {
                log(`✗ GLTF模型加载失败: ${error}`, 'error');
            });
        }
        
        function loadAmapAPI() {
            const script = document.createElement('script');
            script.src = 'https://webapi.amap.com/maps?v=1.4.9&key=51302baed58ef2d9c48e713230aee1a4&plugin=AMap.GltfLoader,Map3D';
            script.onload = function() {
                log('高德地图API加载完成', 'success');
                testGltfLoad();
            };
            script.onerror = function() {
                log('高德地图API加载失败', 'error');
            };
            document.head.appendChild(script);
        }
        
        function openMainApp() {
            window.open('/', '_blank');
        }
        
        // 页面加载时自动检查状态
        window.onload = function() {
            log('调试工具启动', 'info');
            checkSystemStatus();
        };
    </script>
</body>
</html>
